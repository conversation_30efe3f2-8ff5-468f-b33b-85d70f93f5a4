package com.natergy.ni.dmcrm.kafkalistener;

import cn.natergy.message.annotation.NatergyMessageListener;
import com.natergy.ni.dmcrm.kafkalistener.message.KafkaOrderDelMessage;
import com.natergy.ni.dmcrm.kafkalistener.message.KafkaOrderMessage;
import com.natergy.ni.dmcrm.service.ISellDmcrmOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;

import static com.natergy.ni.dmcrm.constant.OrderKafkaGroupConstants.CRM_DMCRM_ORDER_GROUP;
import static com.natergy.ni.dmcrm.constant.OrderKafkaTopicsConstants.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KafkaDmOrderMessageConsumer {

	private final ISellDmcrmOrderService sellDmcrmOrderService;


	public KafkaDmOrderMessageConsumer(ISellDmcrmOrderService sellDmcrmOrderService) {
		this.sellDmcrmOrderService = sellDmcrmOrderService;
	}

	@NatergyMessageListener(topics = CRM_DMCRM_ORDER_CREATE, groupId = CRM_DMCRM_ORDER_GROUP, idempotentKey = "order.id")
	public void createOrder(KafkaOrderMessage message) {
		log.info("create order receive message: {}", message);
		sellDmcrmOrderService.createSaleOrder(message);
	}


	@NatergyMessageListener(topics = CRM_DMCRM_ORDER_UPDATE, groupId = CRM_DMCRM_ORDER_GROUP)
	public void updateOrder(KafkaOrderMessage message) {
		log.info("update order receive message: {}", message);
		sellDmcrmOrderService.updateSaleOrder(message);
	}


	@NatergyMessageListener(topics = CRM_DMCRM_ORDER_DELETE, groupId = CRM_DMCRM_ORDER_GROUP)
	public void delete(KafkaOrderDelMessage order) {
		log.info("delete order receive message: {}", order);
		sellDmcrmOrderService.deleteLogic(Collections.singletonList(order.getId()));
	}

}
