package com.natergy.ni.ofc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.natergy.ni.ofc.dto.OfcCarScheduleCountDTO;
import com.natergy.ni.ofc.entity.OfcCarMaintainanceEntity;
import com.natergy.ni.ofc.entity.OfcCarScheduleEntity;
import com.natergy.ni.ofc.service.IOfcCarMaintainanceRecordService;
import com.natergy.ni.ofc.service.IOfcCarMaintainanceSetService;
import com.natergy.ni.ofc.service.IOfcCarScheduleService;
import com.natergy.ni.ofc.vo.OfcCarMaintainanceRecordVO;
import com.natergy.ni.ofc.vo.OfcCarMaintainanceVO;
import com.natergy.ni.ofc.vo.OfcCarScheduleVO;
import com.natergy.ni.ofc.wrapper.OfcCarScheduleWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.log.LogOptRecord;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@RestController
@AllArgsConstructor
@RequestMapping("ni/ofc/car/schedule")
@Api(value = "办公室管理-车辆调度", tags = "车辆调度接口")
public class OfcCarScheduleController extends BladeController {

	private final IOfcCarScheduleService ofcCarScheduleService;
	private final IOfcCarMaintainanceRecordService ofcCarMaintainanceRecordService;
	private final IOfcCarMaintainanceSetService ofcCarMaintainanceSetService;
	public static final String MODULE = "ni_ofc_carSchedule_ofcCarSchedule";

	/**
	 * 车辆调度 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入ofcCarScheduleVO")
	public R<OfcCarScheduleVO> detail(OfcCarScheduleEntity ofcCarScheduleEntity) {
		OfcCarScheduleEntity detail = ofcCarScheduleService.getOne(Condition.getQueryWrapper(ofcCarScheduleEntity));
		OfcCarScheduleVO detailVo = OfcCarScheduleWrapper.build().entityVO(detail);
		return R.data(detailVo);
	}

	/**
	 * 车辆调度 详情App
	 */
	@GetMapping("/detailApp")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "id")
	public R<OfcCarScheduleEntity> detailApp(@RequestParam("id") String id) {
		OfcCarScheduleEntity detail = ofcCarScheduleService.getById(id);
		return R.data(detail);
	}

	/**
	 * 车辆调度 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入ofcCarScheduleVO")
	public R<IPage<OfcCarScheduleVO>> list(OfcCarScheduleVO ofcCarScheduleVO, Query query) {
		IPage<OfcCarScheduleVO> pages = ofcCarScheduleService
			.selectOfcCarSchedulePage(Condition.getPage(query), ofcCarScheduleVO);
		return R.data(pages);
	}

	/**
	 * 车辆调度 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入ofcCarScheduleVO")
	public R<IPage<OfcCarScheduleVO>> page(OfcCarScheduleVO ofcCarScheduleVO, Query query, BladeUser bladeUser) {
		IPage<OfcCarScheduleVO> pages = ofcCarScheduleService.getSchedulePage(ofcCarScheduleVO,Condition.getPage(query),bladeUser);
		return R.data(pages);
	}

	/**
	 * 车辆调度 自定义分页
	 */
	@GetMapping("/pageApp")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入ofcCarScheduleVO")
	public R<IPage<OfcCarScheduleVO>> pageApp(OfcCarScheduleVO ofcCarScheduleVO, Query query, BladeUser bladeUser) {
		IPage<OfcCarScheduleVO> pages = ofcCarScheduleService.getSchedulePageApp(ofcCarScheduleVO, Condition.getPage(query), bladeUser);
		return R.data(pages);
	}

	/**
	 * 车辆调度 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@LogOptRecord(module = MODULE, businessId = "{#ofcCarScheduleVO.id}", context = "数据新增")
	@ApiOperation(value = "新增", notes = "传入ofcCarScheduleVO")
	public R save(@Valid @RequestBody OfcCarScheduleVO ofcCarScheduleVO) {
//		ofcCarScheduleVO.setUserDept(AuthUtil.getDeptId());
		return R.status(ofcCarScheduleService.save(ofcCarScheduleVO));
	}
	/**
	 * 车辆调度 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入ofcCarScheduleVO")
	public R update(@Valid @RequestBody OfcCarScheduleVO ofcCarScheduleVO) {
//		ofcCarScheduleVO.setUserDept(AuthUtil.getDeptId());
		return R.status(ofcCarScheduleService.updateById(ofcCarScheduleVO));
	}

	/**
	 * 车辆调度 车辆信息更新
	 */
	@PostMapping("/updateApp")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入ofcCarScheduleVO")
	public R updateApp(@Valid @RequestBody OfcCarScheduleVO ofcCarScheduleVO) {
		OfcCarScheduleEntity carScheduleEntity = ofcCarScheduleService.getById(ofcCarScheduleVO.getId());
		carScheduleEntity.setRestOil(ofcCarScheduleVO.getRestOil());

		float restOil = ofcCarScheduleVO.getRestOil();
		float oilConsumption = carScheduleEntity.getOilConsumption();
		float availableMileage = restOil / oilConsumption * 100;
		//计算可行驶里程
		carScheduleEntity.setAvailableMileage(availableMileage);
		carScheduleEntity.setRestOil(restOil);

		carScheduleEntity.setMileage(ofcCarScheduleVO.getMileage());
		carScheduleEntity.setPosition(ofcCarScheduleVO.getPosition());
		if (StringUtil.isNotBlank(ofcCarScheduleVO.getRemark())){
			carScheduleEntity.setRemark(ofcCarScheduleVO.getRemark());
		}
		if (ofcCarScheduleVO.getCarImage() != null){
			carScheduleEntity.setCarImage(ofcCarScheduleVO.getCarImage());
		}
		return R.status(ofcCarScheduleService.saveOrUpdate(carScheduleEntity));
	}
	/**
	 * 车辆调度 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(ofcCarScheduleService.deleteLogic(Func.toLongList(ids)));
	}

//	/**
//	 * 车辆调度 获取调度编号
//	 */
//	@GetMapping("/getSerialNo")
//	@ApiOperationSupport(order = 7)
//	public R getSerialNo(@RequestParam String id) {
//		OfcCarScheduleEntity ofcCarScheduleEntity = ofcCarScheduleService.getById(id);
//		return R.data(ofcCarScheduleEntity.getSerialNo());
//	}

	//权限处理
	//1.车辆空闲
	//2.车辆已借出
	//3.车辆已预约（只有到了时间，没有出借的时候才改变状态）
	// 			==> 3.1车辆预约的同时已经出借
	// 					==> 3.1.1在预约时间段之前还车、3.1.2在预约时间段之前无法还车(预约失败，无法借车，发送通知)
	//			==> 3.2车辆出借时预约期到了（借车人发送通知）
	//			==> 3.3在预约时间段内其他人无法借车


	/**
	 * 获取各车辆状态数
	 */
	@PostMapping("/getCarStatusNumber")
	public R getCarStatusNumber() {
		QueryWrapper<OfcCarScheduleEntity> wrapper = new QueryWrapper<>();
		List<OfcCarScheduleEntity> carScheduleEntityList = ofcCarScheduleService.list(wrapper);
		OfcCarScheduleCountDTO ofcCarScheduleCountDTO = new OfcCarScheduleCountDTO();
		ofcCarScheduleCountDTO.setCarTotal(carScheduleEntityList.size());
		int idleCar = 0;
		int lendCar = 0;
		int appointCar = 0;
		for (OfcCarScheduleEntity carScheduleEntity : carScheduleEntityList){
			if (carScheduleEntity.getCondition() == OfcCarScheduleEntity.IDLE){
				idleCar += 1;
			} else if (carScheduleEntity.getCondition() == OfcCarScheduleEntity.LEND ) {
				lendCar += 1;
			} else if (carScheduleEntity.getCondition() == OfcCarScheduleEntity.BEFORE_APPOINT || carScheduleEntity.getCondition() == OfcCarScheduleEntity.APPOINT) {
				appointCar += 1;
			} else if (carScheduleEntity.getCondition() == OfcCarScheduleEntity.LEND_AND_BEFORE_APPOINT) {
				lendCar += 1;
				appointCar += 1;
			}

		}
		ofcCarScheduleCountDTO.setIdleCar(idleCar);
		ofcCarScheduleCountDTO.setLendCar(lendCar);
		ofcCarScheduleCountDTO.setAppointCar(appointCar);
		return R.data(ofcCarScheduleCountDTO);
	}

	/**
	 * 获取办公室电话号码
	 */
	@GetMapping("/getOfcPhoneNumber")
	public R getOfcPhoneNumber() {
		String phoneNumber = ParamCache.getValue("ni.ofc.ofcCarSchedule.ofcPhoneNumber");
		return R.data(phoneNumber);
	}

	/**
	 * 车辆状况重置
	 */
	@PostMapping("/resetCar")
	public R resetCar (@RequestParam String id) {

		return R.data(ofcCarScheduleService.resetCar(id));
	}

	/**
	 * 车辆保养界面
	 */
	@GetMapping("/maintenance/recordPage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入ofcCarScheduleVO")
	public R<IPage<OfcCarMaintainanceRecordVO>> maintenanceRecordPage(OfcCarMaintainanceRecordVO ofcCarMaintainanceRecordVO, Query query) {
		IPage<OfcCarMaintainanceRecordVO> pages = ofcCarMaintainanceRecordService.selectOfcCarMaintainanceRecordPage(Condition.getPage(query),ofcCarMaintainanceRecordVO);
		return R.data(pages);
	}
	/**
	 * 车辆保养登记
	 */
	@PostMapping("/maintenance/carMaintenanceSubmit")
	public R carMaintenanceSubmit(@RequestBody OfcCarMaintainanceRecordVO ofcCarMaintainanceRecordVO) {
		QueryWrapper<OfcCarMaintainanceEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceRecordVO.getCarId());
		OfcCarMaintainanceEntity ofcCarMaintainanceEntity = ofcCarMaintainanceSetService.getOne(queryWrapper);
		// 计算下次保养日期
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.YEAR, ofcCarMaintainanceEntity.getMaintenanceYear().intValue()); // 加上保养年份
		Date nextMaintenanceDate = calendar.getTime();

		if (ofcCarMaintainanceEntity != null) {
			UpdateWrapper<OfcCarMaintainanceEntity> updateWrapper = new UpdateWrapper<>();
			if (ofcCarMaintainanceEntity.getType().equals(OfcCarMaintainanceEntity.TIME_METHOD)) {//按时间提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceEntity.getCarId())
					.set(OfcCarMaintainanceEntity::getInitialDate, new Date())//初始日期 = 当前日期
					.set(OfcCarMaintainanceEntity::getNextMaintenanceDate, nextMaintenanceDate);//下次保养日期 = 初试日期 + 保养年份
			} else if (ofcCarMaintainanceEntity.getType().equals(OfcCarMaintainanceEntity.MILEAGE_METHOD)) {//按里程提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceEntity.getCarId())
					.set(OfcCarMaintainanceEntity::getInitialMileage, ofcCarMaintainanceRecordVO.getMileage())//初始保养里程 = 当前行驶里程
					.set(OfcCarMaintainanceEntity::getNextMaintenanceMileage, ofcCarMaintainanceRecordVO.getMileage().add(ofcCarMaintainanceEntity.getMaintenanceMileage()));//下次保养里程 = 初始里程 + 保养里程
			} else if (ofcCarMaintainanceEntity.getType().equals(OfcCarMaintainanceEntity.TIME_AND_MILEAGE_METHOD)) {//按时间和里程提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceEntity.getCarId())
					.set(OfcCarMaintainanceEntity::getInitialDate, ofcCarMaintainanceEntity.getInitialDate())//初始日期 = 当前日期
					.set(OfcCarMaintainanceEntity::getInitialMileage,ofcCarMaintainanceRecordVO.getMileage())//初始里程 = 当前行驶里程
					.set(OfcCarMaintainanceEntity::getNextMaintenanceDate, nextMaintenanceDate)//下次保养日期 = 初试日期 + 保养年份
					.set(OfcCarMaintainanceEntity::getNextMaintenanceMileage, ofcCarMaintainanceRecordVO.getMileage().add(ofcCarMaintainanceEntity.getMaintenanceMileage()));//下次保养里程 = 初始里程 + 保养里程
			}
			ofcCarMaintainanceSetService.update(updateWrapper);
		}

		return R.data(ofcCarMaintainanceRecordService.saveOrUpdate(ofcCarMaintainanceRecordVO));
	}
	/**
	 * 车辆保养设置界面
	 */
	@GetMapping("/maintenance/setPage")
	public R<IPage<OfcCarMaintainanceVO>> maintenanceSetPage(OfcCarMaintainanceVO ofcCarMaintainanceVO, Query query) {
		IPage<OfcCarMaintainanceVO> pages = ofcCarMaintainanceSetService.selectOfcCarMaintainanceSetPage(Condition.getPage(query),ofcCarMaintainanceVO);
		return R.data(pages);
	}
	/**
	 * 车辆保养参数配置新增
	 */
	@PostMapping("/maintenance/setSubmit")
	public R carMaintenanceSetSubmit(@RequestBody OfcCarMaintainanceVO ofcCarMaintainanceVO) {
		QueryWrapper<OfcCarMaintainanceEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceVO.getCarId());
		OfcCarMaintainanceEntity ofcCarMaintainanceEntity = ofcCarMaintainanceSetService.getOne(queryWrapper);
		if (ofcCarMaintainanceEntity != null) {
			UpdateWrapper<OfcCarMaintainanceEntity> updateWrapper = new UpdateWrapper<>();
			if (ofcCarMaintainanceVO.getType().equals(OfcCarMaintainanceEntity.TIME_METHOD)) {//按时间提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceVO.getCarId())
					.set(OfcCarMaintainanceEntity::getMaintenanceYear, ofcCarMaintainanceVO.getMaintenanceYear())
					.set(OfcCarMaintainanceEntity::getInitialDate, ofcCarMaintainanceVO.getInitialDate())
					.set(OfcCarMaintainanceEntity::getNextMaintenanceDate, ofcCarMaintainanceVO.getNextMaintenanceDate())
					.set(OfcCarMaintainanceEntity::getNextMaintenanceMileage, null)
					.set(OfcCarMaintainanceEntity::getInitialMileage,null)
					.set(OfcCarMaintainanceEntity::getMaintenanceMileage,null);
			} else if (ofcCarMaintainanceVO.getType().equals(OfcCarMaintainanceEntity.MILEAGE_METHOD)) {//按里程提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceVO.getCarId())
					.set(OfcCarMaintainanceEntity::getMaintenanceYear, null)
					.set(OfcCarMaintainanceEntity::getInitialDate, null)
					.set(OfcCarMaintainanceEntity::getNextMaintenanceDate, null)
					.set(OfcCarMaintainanceEntity::getNextMaintenanceMileage, ofcCarMaintainanceVO.getNextMaintenanceMileage())
					.set(OfcCarMaintainanceEntity::getInitialMileage,ofcCarMaintainanceVO.getInitialMileage())
					.set(OfcCarMaintainanceEntity::getMaintenanceMileage,ofcCarMaintainanceVO.getMaintenanceMileage());
			} else if (ofcCarMaintainanceVO.getType().equals(OfcCarMaintainanceEntity.TIME_AND_MILEAGE_METHOD)) {//按时间和里程提醒
				updateWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, ofcCarMaintainanceVO.getCarId())
					.set(OfcCarMaintainanceEntity::getMaintenanceYear, ofcCarMaintainanceVO.getMaintenanceYear())
					.set(OfcCarMaintainanceEntity::getInitialDate, ofcCarMaintainanceVO.getInitialDate())
					.set(OfcCarMaintainanceEntity::getNextMaintenanceDate, ofcCarMaintainanceVO.getNextMaintenanceDate())
					.set(OfcCarMaintainanceEntity::getNextMaintenanceMileage, ofcCarMaintainanceVO.getNextMaintenanceMileage())
					.set(OfcCarMaintainanceEntity::getInitialMileage,ofcCarMaintainanceVO.getInitialMileage())
					.set(OfcCarMaintainanceEntity::getMaintenanceMileage,ofcCarMaintainanceVO.getMaintenanceMileage());
			}
			return R.data(ofcCarMaintainanceSetService.update(updateWrapper));
		} else {
			return R.data(ofcCarMaintainanceSetService.save(ofcCarMaintainanceVO));
		}

	}

	/**
	 * 车辆保养参数配置新增
	 */
	@GetMapping("/maintenance/setDetail")
	public R carMaintenanceSetDetail(@RequestParam Long carId) {
		QueryWrapper<OfcCarMaintainanceEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(OfcCarMaintainanceEntity::getCarId, carId);
		OfcCarMaintainanceEntity ofcCarMaintainanceEntity = ofcCarMaintainanceSetService.getOne(queryWrapper);
		if (ofcCarMaintainanceEntity != null) {
			return R.data(ofcCarMaintainanceEntity);
		}
		return R.status(false);
	}

	/**
	 * 获取车辆管理员
	 */
	@GetMapping("/maintenance/getCarAdmin")
	public R getCarAdmin(@RequestParam Long userId) {
		return R.data(ofcCarMaintainanceSetService.getCarAdmin(userId));
	}


}
