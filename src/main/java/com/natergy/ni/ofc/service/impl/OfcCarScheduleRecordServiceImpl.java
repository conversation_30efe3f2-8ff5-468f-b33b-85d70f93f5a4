package com.natergy.ni.ofc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.ofc.entity.OfcCarScheduleEntity;
import com.natergy.ni.ofc.entity.OfcCarScheduleRecordEntity;
import com.natergy.ni.ofc.mapper.OfcCarScheduleMapper;
import com.natergy.ni.ofc.mapper.OfcCarScheduleRecordMapper;
import com.natergy.ni.ofc.service.IOfcCarScheduleRecordService;
import com.natergy.ni.ofc.service.IOfcCarScheduleService;
import com.natergy.ni.ofc.vo.OfcCarScheduleRecordVO;
import com.natergy.ni.ofc.vo.OfcCarScheduleVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.system.entity.User;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OfcCarScheduleRecordServiceImpl extends
	BaseServiceImpl<OfcCarScheduleRecordMapper, OfcCarScheduleRecordEntity> implements IOfcCarScheduleRecordService {

	@Override
	public IPage<OfcCarScheduleRecordVO> selectOfcCarScheduleRecordPage(IPage<OfcCarScheduleRecordVO> page,
																  OfcCarScheduleRecordVO ofcCarScheduleRecordVO) {
		return page.setRecords(baseMapper.selectOfcCarScheduleRecordPage(page, ofcCarScheduleRecordVO));
	}

	@Override
	public IPage<OfcCarScheduleRecordVO> getScheduleRecordPage(OfcCarScheduleRecordVO ofcCarScheduleRecordVO, IPage<OfcCarScheduleRecordEntity> page) {
		QueryWrapper<OfcCarScheduleRecordEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda()
			.eq(ofcCarScheduleRecordVO.getBorrowDate() != null, OfcCarScheduleRecordEntity::getBorrowDate,ofcCarScheduleRecordVO.getBorrowDate())
			.eq(ofcCarScheduleRecordVO.getUserId() != null, OfcCarScheduleRecordEntity::getUserId, ofcCarScheduleRecordVO.getUserId())
//			.eq(ofcCarScheduleRecordVO.getUserDept() != null, OfcCarScheduleRecordEntity::getUserDept, ofcCarScheduleRecordVO.getUserDept())
			.like(ofcCarScheduleRecordVO.getCarNumber() != null, OfcCarScheduleRecordEntity::getCarNumber, ofcCarScheduleRecordVO.getCarNumber())
			.eq(ofcCarScheduleRecordVO.getDriver() != null, OfcCarScheduleRecordEntity::getDriver, ofcCarScheduleRecordVO.getDriver())
			.orderByDesc(OfcCarScheduleRecordEntity::getCreateTime);
		IPage res = page(page,wrapper);

		return res;
	}

	@Override
	public IPage<OfcCarScheduleRecordVO> getScheduleRecordPageApp(OfcCarScheduleRecordVO ofcCarScheduleRecordVO, IPage<OfcCarScheduleRecordEntity> page,Long carId) {
		QueryWrapper<OfcCarScheduleRecordEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OfcCarScheduleRecordEntity::getCarId,carId)
			.orderByDesc(OfcCarScheduleRecordEntity::getCreateTime);
		IPage res = page(page,wrapper);

		//根据借车人id保存其姓名
		List<OfcCarScheduleRecordVO> resList = res.getRecords();
		for (OfcCarScheduleRecordEntity carScheduleRecordEntity : resList){
			if (carScheduleRecordEntity.getUserId() != null){
				User userInfo = UserCache.getUser(carScheduleRecordEntity.getUserId());
				carScheduleRecordEntity.setUserName(userInfo.getName());
			}
		}

		return res;
	}

	/**
	 * 判断serialNo有无重复，防止清除Redis缓存导致的编号重复问题
	 */
//	@Override
//	public boolean isSerialNoRepeat(String serialNo){
//		QueryWrapper<OfcCarScheduleRecordEntity> wrapper = new QueryWrapper<>();
//		wrapper.lambda().eq(StringUtils.isNotBlank(serialNo),OfcCarScheduleRecordEntity::getSerialNo,serialNo);
//		List<OfcCarScheduleRecordEntity> entityList = this.list(wrapper);
//		if (entityList.size() == 0){
//			return true;
//		} else return false;
//	}

}
