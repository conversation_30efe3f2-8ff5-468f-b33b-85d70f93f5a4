package com.natergy.ni.sync.yonyou.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.natergy.ni.base.entity.SupplierInfo;
// import com.sun.org.apache.xpath.internal.operations.Bool; // 注释掉：该包不存在
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/2/4 15:35
 */
@NoArgsConstructor
@Data
public class BasicPersonDto implements Serializable {

  private static final long serialVersionUID = 5684816388472223145L;
  /**
   * 供应商编码,示例值(010001)
   */
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String Code;
  /**
   * 供应商名称,示例值(测试)
   */
  private String Name;
  /**
   * 部门编号
   */
  private String DepartmentCode;
  /**
   * 部门名称
   */
  private String DepartmentName;

  private Boolean IsBusinessPerson;

}
