package com.natergy.ni.retail.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

// import sun.misc.BASE64Decoder; // 注释掉：该包不存在
// import sun.misc.BASE64Encoder; // 注释掉：该包不存在

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 */
public class AesUtils {

    private static final Integer KEY_LENGTH = 16;

    /**
     * 加密
     * @param sSrc 文本
     * @param sKey 秘钥
     * @return 加密后文本
     * @throws Exception 加密失败
     */
    public static String encrypt(String sSrc, String sKey) throws Exception {

        // 判断Key是否为16位
        if (sKey.length() != KEY_LENGTH) {
            throw new RuntimeException("请输入16位key");
        }
        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        //"算法/模式/补码方式"
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
        //此处使用BASE64做转码功能，同时能起到2次加密的作用。
        return new BASE64Encoder().encode(encrypted);
    }

    /**
     * 解密
     *
     * @param sSrc 文本
     * @param sKey 秘钥
     * @return 解密后的文本
     * @throws Exception 解密失败
     */
    public static String decrypt(String sSrc, String sKey) throws Exception {
        // 判断Key是否为16位
        if (sKey.length() != KEY_LENGTH) {
            throw new RuntimeException("请输入16位key");
        }
        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        //先用base64解密
        byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);
        byte[] original = cipher.doFinal(encrypted1);
        return new String(original, StandardCharsets.UTF_8);


    }

}
