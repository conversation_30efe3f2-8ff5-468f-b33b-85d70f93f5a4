/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.pa.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.pa.entity.PaAccommodationEntity;
import com.natergy.ni.pa.entity.PaDormitoryEntity;
import com.natergy.ni.pa.vo.PaDormitoryVO;
import java.util.List;

import com.sun.org.apache.xpath.internal.operations.Bool;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

/**
 * 宿舍信息 服务类
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
public interface IPaDormitoryService extends BaseService<PaDormitoryEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param paDormitory
	 * @return
	 */
	IPage<PaDormitoryVO> selectPaDormitoryPage(IPage<PaDormitoryVO> page, PaDormitoryVO paDormitory);

	List<PaDormitoryEntity> getActivatedDormitories();

	List<PaDormitoryVO> wrapEntities2VOs(List<PaDormitoryEntity> dormitories);

	List<PaDormitoryVO>  bedInfoJsonToList(List<PaDormitoryVO> dormitoriesVOs);

	List<PaDormitoryVO>  bedInfoListToJson(List<PaDormitoryVO> dormitoriesVOs);

	List<PaDormitoryVO> getDormitorieVOs(PaDormitoryVO paDormitoryVO);

	List<PaDormitoryVO> getHaveSpaceList();

	String updateBedStaff(PaDormitoryVO paDormitoryVO, int optType/*1:入住 2:退宿*/, int bedIndex, Long employeeId);

	boolean setDormitory(PaAccommodationEntity paAccommodationEntity);
}
