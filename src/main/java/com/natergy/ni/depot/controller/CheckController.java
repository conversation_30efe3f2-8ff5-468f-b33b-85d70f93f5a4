/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.natergy.ni.depot.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.ImmutableMap;
import com.natergy.ni.base.service.ISerialNoService;
import com.natergy.ni.depot.dto.DepotCheckDTO;
import com.natergy.ni.depot.dto.DepotCurrentStockDTO;
import com.natergy.ni.depot.entity.CurrentStockEntity;
import com.natergy.ni.depot.entity.DepotCheckItemEntity;
import com.natergy.ni.depot.service.ICurrentStockService;
import com.natergy.ni.depot.service.IDepotCheckItemService;
import com.natergy.ni.depot.service.impl.CurrentStockServiceImpl;
import com.natergy.ni.depot.vo.DepotCheckItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
// import jdk.nashorn.internal.ir.annotations.Immutable; // 注释掉：该包不存在
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springblade.common.log.LogOptRecord;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.natergy.ni.depot.entity.DepotCheckEntity;
import com.natergy.ni.depot.vo.DepotCheckVO;
import com.natergy.ni.depot.wrapper.DepotCheckWrapper;
import com.natergy.ni.depot.service.IDepotCheckService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 库存盘点 控制器
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ni/depot/check")
@Api(value = "库存盘点", tags = "库存盘点接口")
public class CheckController extends BladeController {

  public static final String MODULE = "ni_depot_check";

  private final IDepotCheckService depotCheckService;
  private final IDepotCheckItemService depotCheckItemService;
  private final ISerialNoService serialNoService;
  private final ICurrentStockService currentStockService;

  /**
   * 库存盘点 详情
   */
  @GetMapping("/detail")
  @ApiOperationSupport(order = 1)
  @ApiOperation(value = "详情", notes = "传入depotCheck")
  public R<DepotCheckVO> detail(DepotCheckEntity depotCheck) {
    DepotCheckEntity detail = depotCheckService.getOne(Condition.getQueryWrapper(depotCheck));
    DepotCheckVO res = DepotCheckWrapper.build().entityVO(detail);
    res.setItems(depotCheckItemService.getListByCheckId(detail.getId()));
    //如果是盘点计划的话，需要显示实时的库存
    if (detail.getType().equals(DepotCheckEntity.TYPE_PLAN)) {
      List<DepotCheckItemVO> planItems = res.getItems().stream()
          .filter(item -> item.getStatus().equals(DepotCheckItemEntity.STATUS_UN)).collect(
              Collectors.toList());
      if (!planItems.isEmpty()) {
        List<CurrentStockEntity> stocks = currentStockService.listByIds(
            planItems.stream().map(DepotCheckItemVO::getCurrentStockId).collect(
                Collectors.toList()));
        Map<Long, CurrentStockEntity> currentStockMap = stocks.stream()
            .collect(Collectors.toMap(CurrentStockEntity::getId, Function.identity()));
        res.getItems().forEach(item -> {
          if (item.getStatus().equals(DepotCheckItemEntity.STATUS_UN)) {
            CurrentStockEntity stock = currentStockMap.get(item.getCurrentStockId());
            if (stock != null) {
              item.setStockNum(stock.getNum());
              item.setStockAmount(stock.getAmount());
            }
          }
        });
      }
    }
    return R.data(res);
  }


  /**
   * 库存盘点 分页
   */
  @GetMapping("/list")
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "分页", notes = "传入depotCheck")
  public R<IPage<DepotCheckVO>> list(DepotCheckEntity depotCheck, Query query) {
    IPage<DepotCheckEntity> pages = depotCheckService.page(Condition.getPage(query),
        Condition.getQueryWrapper(depotCheck));
    return R.data(DepotCheckWrapper.build().pageVO(pages));
  }

  /**
   * 库存盘点 自定义分页
   */
  @GetMapping("/page")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "分页", notes = "传入depotCheck")
  public R<IPage<DepotCheckVO>> page(DepotCheckDTO depotCheck, Query query) {
    IPage<DepotCheckVO> pages = depotCheckService.selectDepotCheckPage(Condition.getPage(query),
        depotCheck);
    return R.data(pages);
  }

  @GetMapping("/count")
  @ApiOperationSupport(order = 3)
  @ApiOperation(value = "分页", notes = "传入depotCheck")
  public R<Map<String, Long>> count() {
    Long submitCount = depotCheckService.count(Wrappers.<DepotCheckEntity>lambdaQuery()
        .eq(DepotCheckEntity::getStatus, DepotCheckEntity.STATUS_SUBMIT));
    Long checkingCount = depotCheckService.count(Wrappers.<DepotCheckEntity>lambdaQuery()
        .eq(DepotCheckEntity::getStatus, DepotCheckEntity.STATUS_CHECKING));
    Long checkedCount = depotCheckService.count(Wrappers.<DepotCheckEntity>lambdaQuery()
        .eq(DepotCheckEntity::getStatus, DepotCheckEntity.STATUS_CHECKED));
    return R.data(
        ImmutableMap.of("submit", submitCount, "checking", checkingCount, "checked", checkedCount));
  }

  /**
   * 库存盘点 新增
   */
  @PostMapping("/save")
  @ApiOperationSupport(order = 4)
  @ApiOperation(value = "新增", notes = "传入depotCheck")
  public R save(@Valid @RequestBody DepotCheckVO depotCheck) {
    if (StringUtils.isBlank(depotCheck.getSerialNo())) {
      String serialNo = serialNoService.gen(MODULE);
      depotCheck.setSerialNo(serialNo);
    } else {
      long num = depotCheckService.count(Wrappers.<DepotCheckEntity>lambdaQuery()
          .eq(DepotCheckEntity::getSerialNo, depotCheck.getSerialNo()));
      Assert.isTrue(num == 0, "编号已存在，请重新输入或选择系统自动生成");
    }
    return R.status(depotCheckService.saveOrUpdate1(depotCheck));
  }

  /**
   * 库存盘点 修改
   */
  @PostMapping("/update")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "修改", notes = "传入depotCheck")
  public R update(@Valid @RequestBody DepotCheckVO depotCheck) {
    if (StringUtils.isBlank(depotCheck.getSerialNo())) {
      String serialNo = serialNoService.gen(MODULE);
      depotCheck.setSerialNo(serialNo);
    }
    return R.status(depotCheckService.saveOrUpdate1(depotCheck));
  }

  /**
   * 库存盘点 删除
   */
  @PostMapping("/remove")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "逻辑删除", notes = "传入ids")
  public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    long num = depotCheckService.count(
        Wrappers.<DepotCheckEntity>lambdaQuery().in(DepotCheckEntity::getId, ids)
            .gt(DepotCheckEntity::getStatus, DepotCheckEntity.STATUS_SUBMIT));
    Assert.isTrue(num == 0, "数据状态错误，请重新选择");
    return R.status(depotCheckService.del(Func.toLongList(ids)));
  }

  @PostMapping("/toVoid")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "作废", notes = "传入ids")
  public R toVoid(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
    long num = depotCheckService.count(
        Wrappers.<DepotCheckEntity>lambdaQuery().in(DepotCheckEntity::getId, ids)
            .notIn(DepotCheckEntity::getStatus, DepotCheckEntity.STATUS_CHECKING,
                DepotCheckEntity.STATUS_CHECKED));
    Assert.isTrue(num == 0, "数据状态错误，请重新选择");
    return R.status(depotCheckService.toVoid(Func.toLongList(ids)));
  }

  /**
   * 库存盘点
   */
  @PostMapping("/check")
  @ApiOperationSupport(order = 7)
  @ApiOperation(value = "盘点状态更新", notes = "传入ids")
  @Transactional(rollbackFor = Exception.class)
  public R check(@ApiParam(value = "主键集合", required = true) @RequestParam Long id) {
    return R.status(depotCheckService.check(id));
  }

  @PostMapping("/checked")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "盘点完成", notes = "传入depotCheck")
  public R checked(@Valid @RequestBody DepotCheckVO depotCheck) {
    return R.status(depotCheckService.checked(depotCheck));
  }

  @PostMapping("/audit")
  @ApiOperationSupport(order = 5)
  @ApiOperation(value = "审核", notes = "传入depotCheck")
  public R audit(Long id) {
    DepotCheckEntity check = depotCheckService.getById(id);
    Assert.isTrue(check.getStatus().equals(DepotCheckEntity.STATUS_CHECKED),
        "盘点状态错误，请刷新后重试");
    return R.status(depotCheckService.auditById(id));
  }
}
