package org.springblade.plugin.workflow.process.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Nullable;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.ExtensionAttribute;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.InclusiveGateway;
import org.flowable.bpmn.model.ParallelGateway;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.persistence.entity.AttachmentEntityImpl;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl;
import org.flowable.engine.impl.util.ExecutionGraphUtil;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Attachment;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.flowable.variable.api.history.HistoricVariableInstanceQuery;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserSearchService;
import org.springblade.plugin.workflow.core.cache.WfProcessCache;
import org.springblade.plugin.workflow.core.constant.WfExtendConstant;
import org.springblade.plugin.workflow.core.constant.WfProcessConstant;
import org.springblade.plugin.workflow.core.entity.FlowProcess;
import org.springblade.plugin.workflow.core.utils.ObjectUtil;
import org.springblade.plugin.workflow.core.utils.WfModelUtil;
import org.springblade.plugin.workflow.core.utils.WfSearchUtil;
import org.springblade.plugin.workflow.core.utils.WfTaskUtil;
import org.springblade.plugin.workflow.design.entity.WfButton;
import org.springblade.plugin.workflow.design.entity.WfModel;
import org.springblade.plugin.workflow.design.mapper.WfModelMapper;
import org.springblade.plugin.workflow.design.service.IWfButtonService;
import org.springblade.plugin.workflow.design.service.IWfSerialService;
import org.springblade.plugin.workflow.ops.service.IWfProxyService;
import org.springblade.plugin.workflow.process.dto.FunctionInfoDTO;
import org.springblade.plugin.workflow.process.entity.WfDraft;
import org.springblade.plugin.workflow.process.entity.WfNotice;
import org.springblade.plugin.workflow.process.model.WfNode;
import org.springblade.plugin.workflow.process.model.WfProcess;
import org.springblade.plugin.workflow.process.model.WfTaskUser;
import org.springblade.plugin.workflow.process.service.IWfCopyService;
import org.springblade.plugin.workflow.process.service.IWfDraftService;
import org.springblade.plugin.workflow.process.service.IWfExpressionService;
import org.springblade.plugin.workflow.process.service.IWfFormValidateService;
import org.springblade.plugin.workflow.process.service.IWfNoticeService;
import org.springblade.plugin.workflow.process.service.IWfProcessService;
import org.springblade.plugin.workflow.process.utils.RegExUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class WfProcessService implements IWfProcessService {

	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final HistoryService historyService;
	private final TaskService taskService;
	private final RepositoryService repositoryService;

	private final IWfCopyService wfCopyService;
	private final IWfSerialService wfSerialService;
	private final IWfNoticeService wfNoticeService;
	private final IWfDraftService wfDraftService;
	private final IWfExpressionService wfExpressionService;
	private final WfModelMapper wfModelService;
	private final IWfProxyService wfProxyService;
	private final IUserSearchService userSearchService;
	private final IWfButtonService buttonService;
	private final IWfFormValidateService wfFormValidateService;


	@Override
	public String startProcessInstanceById(String processDefId, Map<String, Object> variables) {
		return startProcessInstanceById(processDefId, variables, null);
	}

	@Override
	public String startProcessInstanceById(String processDefId, Map<String, Object> variables,
										   String userId) {
		FlowProcess definition = WfProcessCache.getProcessDefinition(processDefId);
		if (definition == null) {
			throw new RuntimeException("查询不到此部署的流程");
		}

		//表单提交进行验证
		wfFormValidateService.formValidate(processDefId, null, null, null, variables);

		if (StringUtils.isBlank(userId)) {
			userId = WfTaskUtil.getTaskUser();
		}
		variables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER, userId);
		User user = UserCache.getUser(Long.valueOf(userId));
		if (user != null) {
			variables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME, user.getRealName());
		}
		// 启动流程
		identityService.setAuthenticatedUserId(userId);
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefId,
			definition.getKey(), variables);
		return handleProcessInstance(processInstance, variables, userId);
	}

	@Override
	public String startProcessInstanceByKey(String processDefKey, String businessKey,
											Map<String, Object> variables) {
		String tenantId = WfTaskUtil.getTenantId();
		String userId = WfTaskUtil.getTaskUser();
		variables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER, userId);
		// 启动流程
		identityService.setAuthenticatedUserId(userId);
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKeyAndTenantId(
			processDefKey, businessKey, variables, tenantId);
		return handleProcessInstance(processInstance, variables, null);
	}

	private String handleProcessInstance(ProcessInstance processInstance,
										 @Nullable Map<String, Object> variables, String userId) {
		if (StringUtil.isBlank(userId)) {
			userId = WfTaskUtil.getTaskUser();
		}
		String processDefId = processInstance.getProcessDefinitionId();
		if (variables == null) {
			variables = new HashMap<>();
		}
		// 业务变量
		Map<String, Object> customVariables = new HashMap<>();
		User user;
		if (StringUtils.isNotBlank(userId) && !"-1".equals(userId)) {
			user = UserCache.getUser(Long.parseLong(userId));
		} else {
			userId = variables.get(WfProcessConstant.TASK_VARIABLE_APPLY_USER) + "";
			if (StringUtils.isBlank(userId)) {
				user = UserCache.getAdmin();
				userId = user.getId() + "";
			} else {
				user = UserCache.getUser(Long.parseLong(userId));
			}
		}
		customVariables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME, user.getName());


		//从上下文里面获取这个流水号，有的话就用，没有的话就自己创建
		Object serialNo = MDC.get(WfProcessConstant.TASK_VARIABLE_SN);

		if (Objects.isNull(serialNo)) {
			// 流水号
			serialNo = wfSerialService.getNextSN(processInstance.getDeploymentId());
		}else{
			MDC.remove(WfProcessConstant.TASK_VARIABLE_SN);
		}

		if (Objects.nonNull(serialNo)) {
			customVariables.put(WfProcessConstant.TASK_VARIABLE_SN, serialNo);
		}
		runtimeService.setVariables(processInstance.getId(), customVariables);

		// 修改流程实例名称，方便查询
		try {
			runtimeService.setProcessInstanceName(processInstance.getId(),
				processInstance.getProcessDefinitionName());
		} catch (Exception ignore) {
			return processInstance.getId();
		}
		// 自动跳过第一节点
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
		String skip = WfModelUtil.getProcessExtensionAttribute(bpmnModel,
			WfExtendConstant.SKIP_FIRST_NODE);
		if (StringUtil.isNotBlank(skip) && "true".equals(skip)) {
			List<Task> taskList = taskService.createTaskQuery()
				.processInstanceId(processInstance.getId())
				.list();
			taskList.forEach(task -> taskService.complete(task.getId()));
		}
		// 指定下一步审核人
		this.handleNextNodeAssignee(processInstance.getId(),
			variables.get(WfProcessConstant.TASK_VARIABLE_ASSIGNEE));
		// 消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(userId)
			.setProcessId(processInstance.getId())
			.setType(WfNotice.Type.START));
		// 处理抄送
		Object copyUser = variables.get(WfProcessConstant.TASK_VARIABLE_COPY_USER);
		if (ObjectUtil.isNotEmpty(copyUser)) {
			List<Task> taskList = taskService.createTaskQuery()
				.processInstanceId(processInstance.getId())
				.list();
			if (!taskList.isEmpty()) {
				Task task = taskList.get(0);
				WfProcess process = new WfProcess();
				process.setAssignee(WfTaskUtil.getTaskUser());
				process.setAssigneeName(WfTaskUtil.getNickName());
				process.setTaskId(task.getId());
				process.setTaskName(
					processInstance.getProcessDefinitionName() + "-" + task.getName());
				process.setProcessInstanceId(processInstance.getId());
				process.setCopyUser(copyUser.toString());
				wfCopyService.resolveCopyUser(process);
			}
		}
		// 删除草稿箱
		Object draftId = variables.get("draftId");
		if (draftId != null) {
			//删除当前使用的草稿
			wfDraftService.update(Wrappers.<WfDraft>lambdaUpdate()
				.in(WfDraft::getId, draftId).set(WfDraft::getIsDeleted, 1));

		}
		return processInstance.getId();
	}

	@Override
	public IPage<WfProcess> selectTaskPage(WfProcess process, Query query) {
		IPage<WfProcess> page = new Page<>();

		String taskUser = WfTaskUtil.getTaskUser();
		String taskGroup = WfTaskUtil.getCandidateGroup();

		long count;
		switch (process.getStatus()) {
			case WfProcessConstant.STATUS_TODO:  // 待办或待签 runtimeService
			case WfProcessConstant.STATUS_CLAIM:
				TaskQuery taskQuery = taskService.createTaskQuery()
					.orderByTaskCreateTime()
					.desc()
					.taskTenantId(WfTaskUtil.getTenantId())
					.active();
				if (WfProcessConstant.STATUS_TODO.equals(process.getStatus())) { // 待办
					taskQuery
//						.taskAssignee(taskUser)
						.taskCandidateOrAssigned(taskUser)
						.taskCandidateGroupIn(Func.toStrList(taskGroup))
					;
				} else { // 待签
					taskQuery.taskCandidateUser(taskUser)
						.taskCandidateGroupIn(Func.toStrList(taskGroup));
				}
				WfSearchUtil.buildSearchQuery(taskQuery, process); // 搜索条件
				count = taskQuery.count();
				if (count > 0) {
					List<WfProcess> list = new LinkedList<>();
					buildTaskList(list, taskQuery, query, WfProcessConstant.STATUS_TODO);
					page.setRecords(list);
				}
				break;
			case WfProcessConstant.STATUS_DONE:  // 已办 historyService
				HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
					.orderByTaskCreateTime()
					.desc()
					.taskAssignee(taskUser)
					.finished()
					.taskTenantId(WfTaskUtil.getTenantId());
				WfSearchUtil.buildSearchQuery(historicTaskInstanceQuery, process); // 搜索条件
				count = historicTaskInstanceQuery.count();
				if (count > 0) {
					List<WfProcess> list = new LinkedList<>();
					buildTaskList(list, historicTaskInstanceQuery, query,
						WfProcessConstant.STATUS_DONE);
					page.setRecords(list);
				}
				break;
			default:
				return page;
		}
		page.setTotal(count);
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());

		return page;
	}


	@Override
	public WfProcess selectToDoTaskPageByUser(WfProcess process, Query query, String taskUser,
											  String status) {

		User user = UserCache.getUser(Long.parseLong(taskUser));

		BladeUser bladeUser = BeanUtil.copy(user, BladeUser.class);

		String taskUserGroup = WfTaskUtil.getCandidateGroupData(bladeUser);

		TaskQuery taskQuery = taskService.createTaskQuery()
			.orderByTaskCreateTime()
			.desc()
			.taskTenantId(bladeUser.getTenantId())
			.processInstanceId(process.getProcessInstanceId())
			.taskAssignee(process.getAssignee())
			.active();
		taskQuery
			.taskCandidateOrAssigned(taskUser)
			.taskCandidateGroupIn(Func.toStrList(taskUserGroup));

		WfSearchUtil.buildSearchQuery(taskQuery, process); // 搜索条件
		List<WfProcess> list = new LinkedList<>();
		buildTaskList(list, taskQuery, query, WfProcessConstant.STATUS_DONE);

		if (list.isEmpty()) {
			throw new RuntimeException("无法查找到对应的流程");
		}

		return list.get(0);
	}

	@Override
	public IPage<WfProcess> selectProcessPage(WfProcess process, Query query) {
		IPage<WfProcess> page = new Page<>();

		String taskUser = WfTaskUtil.getTaskUser();
		HistoricProcessInstanceQuery historyQuery = historyService.createHistoricProcessInstanceQuery()
			.orderByProcessInstanceStartTime()
			.desc();

		if (StringUtil.isNotBlank(process.getStatus())) {
			switch (process.getStatus()) {
				case WfProcessConstant.STATUS_SEND: // 我的请求
					historyQuery.startedBy(taskUser);
					break;
				case WfProcessConstant.STATUS_DONE: // 办结
					historyQuery.involvedUser(taskUser).finished();
					break;
				case WfProcessConstant.STATUS_FINISH: // 结束
					historyQuery
						.processInstanceTenantId(WfTaskUtil.getTenantId())
						.finished();
					break;
			}
		}
		WfSearchUtil.buildSearchQuery(historyQuery, process); // 搜索条件

		long count = historyQuery.count();
		if (count > 0) {
			List<WfProcess> list = new LinkedList<>();
			buildProcessList(list, historyQuery, query);
			page.setRecords(list);
		}
		page.setTotal(count);
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		return page;
	}

	@Async
	@Override
	public Future<List<WfProcess>> historyFlowList(String processInstanceId, String startActivityId,
												   String endActivityId) {

		List<WfProcess> wfProcesses = this.syncHistoryFlowList(processInstanceId, startActivityId,
			endActivityId);

		return new AsyncResult<>(wfProcesses);
	}

	@Override
	public List<WfProcess> syncHistoryFlowList(String processInstanceId, String startActivityId,
											   String endActivityId) {
		List<WfProcess> flowList = new LinkedList<>();
		List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
			.processInstanceId(processInstanceId)
			.orderByHistoricActivityInstanceStartTime().asc()
			.orderByHistoricActivityInstanceEndTime().asc()
			.list();
		List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
		List<Attachment> attachmentList = taskService.getProcessInstanceAttachments(
			processInstanceId);
		boolean start = false;
		Map<String, Integer> activityMap = new HashMap<>(16);
		for (int i = 0; i < historicActivityInstanceList.size(); i++) {
			HistoricActivityInstance historicActivityInstance = historicActivityInstanceList.get(i);
			// 过滤开始节点前的节点
			if (StringUtil.isNotBlank(startActivityId) && startActivityId.equals(
				historicActivityInstance.getActivityId())) {
				start = true;
			}
			if (StringUtil.isNotBlank(startActivityId) && !start) {
				continue;
			}
			// 显示开始节点和结束节点，并且执行人不为空的任务
			if (Arrays.asList(
				WfProcessConstant.USER_TASK,
				WfProcessConstant.START_EVENT,
				WfProcessConstant.END_EVENT,
				WfProcessConstant.SEQUENCE_FLOW,
				WfProcessConstant.HTTP_SERVICE_TASK,
				WfProcessConstant.SHELL_SERVICE_TASK,
				WfProcessConstant.MAIL_SERVICE_TASK
			).contains(historicActivityInstance.getActivityType())) {
				// 给节点增加序号
				activityMap.computeIfAbsent(historicActivityInstance.getActivityId(),
					k -> activityMap.size());
				WfProcess flow = new WfProcess();
				flow.setTaskId(historicActivityInstance.getTaskId());
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flow.setHistoryActivityName(historicActivityInstance.getActivityName());
				flow.setHistoryActivityType(historicActivityInstance.getActivityType());
				flow.setCreateTime(historicActivityInstance.getStartTime());
				flow.setEndTime(historicActivityInstance.getEndTime());
				String durationTime = DateUtil.secondToTime(
					Func.toLong(historicActivityInstance.getDurationInMillis(), 0L) / 1000);
				flow.setHistoryActivityDurationTime(durationTime);
				// 获取流程发起人名称
				if (WfProcessConstant.START_EVENT.equals(
					historicActivityInstance.getActivityType())) {
					List<HistoricProcessInstance> processInstanceList = historyService.createHistoricProcessInstanceQuery()
						.processInstanceId(processInstanceId).orderByProcessInstanceStartTime()
						.asc().list();
					if (!processInstanceList.isEmpty()) {
						if (StringUtil.isNotBlank(processInstanceList.get(0).getStartUserId())) {
							String taskUser = processInstanceList.get(0).getStartUserId();
							User user = UserCache.getUser(WfTaskUtil.getUserId(taskUser));
							if (user != null) {
								flow.setAssignee(historicActivityInstance.getAssignee());
								flow.setAssigneeName(user.getName());
							}
						}
					}
				} else if (WfProcessConstant.USER_TASK.equals(
					historicActivityInstance.getActivityType())) {
					// 获取任务执行人名称
					if (StringUtil.isNotBlank(historicActivityInstance.getAssignee())) {
						User user = UserCache.getUser(
							WfTaskUtil.getUserId(historicActivityInstance.getAssignee()));
						if (user != null) {
							flow.setAssignee(historicActivityInstance.getAssignee());
							flow.setAssigneeName(user.getName());
						}
					} else {
						WfTaskUser taskUser = this.getTaskUser(
							historicActivityInstance.getProcessDefinitionId(),
							historicActivityInstance.getProcessInstanceId(),
							historicActivityInstance.getActivityId());
						List<User> userList = taskUser.getUserList();
						if (ObjectUtil.isNotEmpty(userList)) {
							if (userList.size() == 1) {
								flow.setAssignee(userList.get(0).getId() + "");
								flow.setAssigneeName(userList.get(0).getName());
							} else {
								flow.setAssigneeName(
									userList.stream().map(User::getName)
										.collect(Collectors.joining("/")));
							}
						}
					}
				}

				// 获取意见评论内容/附件
				if (StringUtil.isNotBlank(historicActivityInstance.getTaskId())) {
					List<Comment> comments = new ArrayList<>();
					for (Comment comment : commentList) {
						if (comment.getTaskId().equals(historicActivityInstance.getTaskId())) {
							comments.add(comment);
						}
					}
					flow.setComments(comments);

					List<Attachment> attachments = new ArrayList<>();
					for (Attachment attachment : attachmentList) {
						if (attachment.getTaskId().equals(historicActivityInstance.getTaskId())) {
							attachments.add(attachment);
						}
					}
					flow.setAttachments(attachments);
				}
				flowList.add(flow);
			}
			// 过滤结束节点后的节点
			if (StringUtils.isNotBlank(endActivityId) && endActivityId.equals(
				historicActivityInstance.getActivityId())) {
				boolean temp = false;
				Integer activityNum = activityMap.get(historicActivityInstance.getActivityId());
				// 该活动节点，后续节点是否在结束节点之前，在后续节点中是否存在
				for (int j = i + 1; j < historicActivityInstanceList.size(); j++) {
					HistoricActivityInstance hi = historicActivityInstanceList.get(j);
					Integer activityNumA = activityMap.get(hi.getActivityId());
					boolean numberTemp = activityNumA != null && activityNumA < activityNum;
					boolean equalsTemp = StringUtils.equals(hi.getActivityId(),
						historicActivityInstance.getActivityId());
					if (numberTemp || equalsTemp) {
						temp = true;
					}
				}
				if (!temp) {
					break;
				}
			}
		}
		// 处理未流转到的节点
		if (!historicActivityInstanceList.isEmpty()) {
			String processDefId = historicActivityInstanceList.get(0).getProcessDefinitionId();
			String processInsId = historicActivityInstanceList.get(0).getProcessInstanceId();
			HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceId(processInsId).singleResult();
			if (processInstance.getEndTime() == null) { // 流程结束后不再处理未流转到的节点
				BpmnModel model = repositoryService.getBpmnModel(processDefId);

				List<FlowElement> elements = new ArrayList<>();
				model.getMainProcess().getFlowElements().forEach(flowElement -> {
					if (flowElement instanceof UserTask) {
						WfProcess wfProcess = flowList.stream()
							.filter(flow -> flow.getHistoryActivityId().equals(flowElement.getId()))
							.findFirst()
							.orElse(null);
						if (wfProcess == null) {
							elements.add(flowElement);
						}
					}
				});
				if (!elements.isEmpty()) {
					elements.forEach(element -> {
						WfProcess flow = new WfProcess();
						flow.setHistoryActivityId(element.getId());
						flow.setHistoryActivityName(element.getName());
						flow.setHistoryActivityType(WfProcessConstant.CANDIDATE);
						List<User> userList = this.getTaskUser(processDefId, processInsId,
								element.getId())
							.getUserList();
						if (ObjectUtil.isNotEmpty(userList)) {
							flow.setAssigneeName(
								userList.stream().map(User::getName)
									.collect(Collectors.joining("/")));
						}
						flowList.add(flow);
					});
				}
			}
		}
		return flowList;
	}

	@Async
	@Override
	public Future<WfProcess> detail(String taskId, String assignee, String candidateGroup) {
		WfProcess detail = getDetail(taskId, assignee, candidateGroup);

		return new AsyncResult<>(detail);
	}

	@NotNull
	private WfProcess getDetail(String taskId, String assignee, String candidateGroup) {
		WfProcess process = new WfProcess();

		HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery() // 是否待办
			.taskId(taskId)
			.includeProcessVariables()
//			.includeTaskLocalVariables()
			.includeIdentityLinks()
			.singleResult();
		if (task == null) {
			return process;
		}
		if (StringUtil.isNotBlank(task.getAssignee())) { // 有审核人
			if (assignee.equals(task.getAssignee())) { // 我的任务
				if (task.getEndTime() == null) {
					process.setStatus(WfProcessConstant.STATUS_TODO);
				} else {
					process.setStatus(WfProcessConstant.STATUS_DONE);
				}
			} else {
				process.setStatus(WfProcessConstant.STATUS_DONE);
			}
		} else { // 候选或者已办
			List<? extends IdentityLinkInfo> identityLinks = task.getIdentityLinks();
			// 候选组
			List<String> roles = new ArrayList<>();
			// 候选人
			List<String> userIds = new ArrayList<>();
			identityLinks.forEach(link -> {
				if (StringUtil.isNotBlank(link.getGroupId())) {
					roles.add(link.getGroupId());
				}
				if (StringUtil.isNotBlank(link.getUserId())) {
					userIds.add(link.getUserId());
				}
			});
			List<String> candidateGroups = Arrays.asList(candidateGroup.split(","));
			if ((userIds.contains(assignee) || roles.stream().anyMatch(candidateGroups::contains))
				&& task.getEndTime() == null) { // 是否选人或候选组
				process.setStatus(WfProcessConstant.STATUS_TODO);
			} else {
				process.setStatus(WfProcessConstant.STATUS_DONE);
			}
		}

		process.setIsMultiInstance(
			this.isMultiInstance(task.getTaskDefinitionKey(), task.getProcessDefinitionId()));
		process.setTaskId(task.getId());
		process.setTaskDefinitionKey(task.getTaskDefinitionKey());
		process.setTaskName(task.getName());
		process.setAssignee(task.getAssignee());
		process.setCreateTime(task.getCreateTime());
		process.setExecutionId(task.getExecutionId());
		process.setHistoryTaskEndTime(task.getEndTime());
		Map<String, Object> variables = task.getProcessVariables();
		variables.putAll(task.getTaskLocalVariables());
		process.setVariables(variables);
		process.setProcessInstanceId(task.getProcessInstanceId());

		FlowProcess processDefinition = WfProcessCache.getProcessDefinition(
			task.getProcessDefinitionId());
		if (processDefinition != null) {
			process.setProcessDefinitionId(processDefinition.getId());
			process.setProcessDefinitionName(processDefinition.getName());
			process.setProcessDefinitionKey(processDefinition.getKey());
			process.setKey(processDefinition.getKey());
			process.setProcessDefinitionVersion(processDefinition.getVersion());
			process.setCategory(processDefinition.getCategory());
		}
		HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
			.processInstanceId(task.getProcessInstanceId())
			.singleResult();
		User starter = UserCache.getUser(Long.valueOf(processInstance.getStartUserId()));
		if (starter != null) {
			process.setStartUsername(starter.getName());
		}
		process.setIsOwner(assignee.equals(processInstance.getStartUserId()));
		process.setIsReturnable(this.isReturnable(taskId, assignee));

		// 流程状态
		this.setProcessStatus(process, processInstance);
//    if (!process.getProcessIsFinished().equals(WfProcessConstant.STATUS_FINISHED)
//        && assignee.equals(processInstance.getStartUserId())) {
//      process.setIsReturnable(true);
//    }
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

		try {
			WfModel model = wfModelService.selectOne(
				new LambdaQueryWrapper<WfModel>().eq(WfModel::getModelKey,
					processDefinition.getKey()));
			if (model == null) {
				throw new RuntimeException();
			}
			process.setXml(model.getXml());
		} catch (Exception e) {
			process.setXml(new String(new BpmnXMLConverter().convertToXML(bpmnModel)));
		}

		if (process.getStatus().equals(WfProcessConstant.STATUS_TODO)) { // 待办再查询扩展属性
			String taskKey = task.getTaskDefinitionKey();

			// 隐藏评论附件选项
			String hideAttachment = WfModelUtil.getUserTaskExtensionAttribute(taskKey, bpmnModel,
				WfExtendConstant.HIDE_ATTACHMENT);
			if (StringUtil.isNotBlank(hideAttachment) && "true".equals(hideAttachment)) {
				process.setHideAttachment(true);
			}

			// 隐藏抄送人选项
			String hideCopy = WfModelUtil.getUserTaskExtensionAttribute(taskKey, bpmnModel,
				WfExtendConstant.HIDE_COPY);
			if (StringUtil.isNotBlank(hideCopy) && "true".equals(hideCopy)) {
				process.setHideCopy(true);
			}

			// 隐藏下一步审核人选项
			String hideExamine = WfModelUtil.getUserTaskExtensionAttribute(taskKey, bpmnModel,
				WfExtendConstant.HIDE_EXAMINE);
			if (StringUtil.isNotBlank(hideExamine) && "true".equals(hideExamine)) {
				process.setHideExamine(true);
			}

			// 默认抄送人
			List<ExtensionElement> copyUsers = WfModelUtil.getUserTaskExtensionElements(taskKey,
				bpmnModel, WfExtendConstant.COPY_USER);
			if (copyUsers != null && copyUsers.size() > 0) {
				List<String> values = new ArrayList<>();
				List<String> texts = new ArrayList<>();
				copyUsers.forEach(copyUser -> {
					String value = copyUser.getAttributes().get("value").get(0).getValue();
					String text = copyUser.getAttributes().get("text").get(0).getValue();
					if (StringUtil.isNoneBlank(value, text)) {
						values.add(value);
						texts.add(text);
					}
				});
				if (values.size() > 0 && texts.size() > 0) {
					process.setCopyUser(String.join(",", values));
					process.setCopyUserName(String.join(",", texts));
				}
			}
		}

		return process;
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object completeTask(WfProcess process) {
		String taskId = process.getTaskId();

		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}

		//审核通过才行进行表单验证
		if (process.isPass()) {
			//表单提交进行验证
			wfFormValidateService.formValidate(task.getProcessDefinitionId(), task.getProcessInstanceId(),
				task.getTaskDefinitionKey(), task.getId(), process.getVariables());
		}

		taskService.setVariable(task.getId(), WfProcessConstant.PASS_KEY, process.isPass());
		process.setTaskName(process.getProcessDefinitionName() + "-" + task.getName());
		process.setAssigneeName(WfTaskUtil.getNickName());
		process.setTaskDefinitionKey(task.getTaskDefinitionKey());

		runtimeService.setVariable(task.getProcessInstanceId(), "wf_latest_task_assignee",
			WfTaskUtil.getTaskUser());
		if (process.isPass()) { // 审核通过
			this.passTask(process, task);
		} else { // 审核不通过
			this.rejectTask(process);
		}

		// 处理抄送
		if (StringUtil.isNotBlank(process.getCopyUser())) {
			process.setAssignee(WfTaskUtil.getTaskUser());
			wfCopyService.resolveCopyUser(process);
		}

		return R.success("操作成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#processInstanceId")
	public Object completeAllTask(String processInstanceId) {

		List<Task> taskList = taskService.createTaskQuery()
			.processInstanceId(processInstanceId)
			.active()
			.list();
		taskList.forEach(task -> taskService.complete(task.getId()));
		return R.success("操作成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object transferTask(WfProcess process) {
		String taskId = process.getTaskId();
		String acceptUser = process.getAssignee();
		String comment = process.getComment();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		if (StringUtil.isNotBlank(comment)) {
			User fromUser = UserCache.getUser(Long.valueOf(WfTaskUtil.getTaskUser()));
			User toUser = UserCache.getUser(Long.valueOf(acceptUser));
			if (fromUser != null && toUser != null) {
				comment = fromUser.getName() + "→" + toUser.getName() + "：" + comment;
			}
			taskService.addComment(taskId, task.getProcessInstanceId(),
				WfProcessConstant.COMMENT_TYPE_TRANSFER, comment);
		}
		// 评论附件
		List<AttachmentEntityImpl> attachment = process.getAttachment();
		if (ObjectUtil.isNotEmpty(attachment)) {
			identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
			String finalComment = comment;
			attachment.forEach(att -> taskService.saveAttachment(
				taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_TRANSFER, taskId,
					task.getProcessInstanceId(), att.getName(), finalComment, att.getUrl())));
		}
		taskService.removeVariable(taskId,
			WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE); // 删除撤回/驳回标记
		taskService.setOwner(taskId, WfTaskUtil.getTaskUser());
		taskService.setAssignee(taskId, acceptUser);

		// 处理抄送
		if (StringUtil.isNotBlank(process.getCopyUser())) {
			process.setTaskName(process.getProcessDefinitionName() + "-" + task.getName());
			process.setAssigneeName(WfTaskUtil.getNickName());
			process.setAssignee(WfTaskUtil.getTaskUser());
			wfCopyService.resolveCopyUser(process);
		}

		// 处理消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(WfTaskUtil.getTaskUser())
			.setToUserId(acceptUser)
			.setProcessId(task.getProcessInstanceId())
			.setTaskId(taskId)
			.setComment(comment)
			.setType(WfNotice.Type.TRANSFER));
		return R.success("转办成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object delegateTask(WfProcess process) {
		String taskId = process.getTaskId();
		String acceptUser = process.getAssignee();
		String comment = process.getComment();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		if (StringUtil.isNotBlank(comment)) {
			User fromUser = UserCache.getUser(Long.valueOf(WfTaskUtil.getTaskUser()));
			User toUser = UserCache.getUser(Long.valueOf(acceptUser));
			if (fromUser != null && toUser != null) {
				comment = fromUser.getName() + "→" + toUser.getName() + "：" + comment;
			}
			taskService.addComment(taskId, task.getProcessInstanceId(),
				WfProcessConstant.COMMENT_TYPE_DELEGATE, comment);
		}
		// 评论附件
		List<AttachmentEntityImpl> attachment = process.getAttachment();
		if (ObjectUtil.isNotEmpty(attachment)) {
			identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
			String finalComment = comment;
			attachment.forEach(att -> taskService.saveAttachment(
				taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_DELEGATE, taskId,
					task.getProcessInstanceId(), att.getName(), finalComment, att.getUrl())));
		}
		taskService.removeVariable(taskId,
			WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE); // 删除撤回/驳回标记
		taskService.setOwner(taskId, WfTaskUtil.getTaskUser());
		taskService.delegateTask(taskId, acceptUser);

		// 处理抄送
		if (StringUtil.isNotBlank(process.getCopyUser())) {
			process.setTaskName(process.getProcessDefinitionName() + "-" + task.getName());
			process.setAssigneeName(WfTaskUtil.getNickName());
			process.setAssignee(WfTaskUtil.getTaskUser());
			wfCopyService.resolveCopyUser(process);
		}

		// 处理消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(WfTaskUtil.getTaskUser())
			.setToUserId(acceptUser)
			.setProcessId(task.getProcessInstanceId())
			.setTaskId(taskId)
			.setComment(comment)
			.setType(WfNotice.Type.DELEGATE));
		return R.success("委托成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object claimTask(String taskId) {
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		taskService.claim(taskId, WfTaskUtil.getTaskUser());
		return R.success("签收成功");
	}

	@Override
	public List<WfNode> getBackNodes(WfProcess wfProcess) {
		String taskId = wfProcess.getTaskId();
		String processInstanceId = wfProcess.getProcessInstanceId();
		HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(taskId)
			.singleResult();

		String currActId = task.getTaskDefinitionKey();
		String processDefinitionId = task.getProcessDefinitionId();
		Process process = repositoryService.getBpmnModel(processDefinitionId).getMainProcess();
		FlowNode currentFlowElement = (FlowNode) process.getFlowElement(currActId, true);
		Preconditions.checkState(StringUtils.isNotBlank(processInstanceId),
			"processInstanceId参数获取失败：" + taskId);
		List<HistoricActivityInstance> activities = historyService.createHistoricActivityInstanceQuery()
			.processInstanceId(processInstanceId).finished()
			.orderByHistoricActivityInstanceStartTime()
			.asc().list();
		List<String> activityIds =
			activities.stream().filter(
					activity -> activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_USER)
						|| activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_EVENT_START))
				.map(HistoricActivityInstance::getActivityId)
				.filter(activityId -> !activityId.equals(currActId)).distinct()
				.collect(Collectors.toList());
		List<WfNode> result = new ArrayList<>();
		for (String activityId : activityIds) {
			FlowNode toBackFlowElement = (FlowNode) process.getFlowElement(activityId, true);
			if (toBackFlowElement != null && ExecutionGraphUtil.isReachable(process,
				toBackFlowElement,
				currentFlowElement, Sets.newHashSet())) {
				WfNode vo = new WfNode();
				vo.setNodeName(toBackFlowElement.getName());
				vo.setNodeId(activityId);
				result.add(vo);
			}
		}
		return result;
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object rollbackTask(WfProcess process) {
		String taskId = process.getTaskId();
		String nodeId = process.getNodeId();
		String comment = process.getComment();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		if (StringUtil.isBlank(task.getAssignee())) {
			taskService.claim(taskId, WfTaskUtil.getTaskUser());
		}

		ActivityInstance targetRealActivityInstance = runtimeService
			.createActivityInstanceQuery()
			.processInstanceId(task.getProcessInstanceId())
			.activityId(nodeId).list().get(0);
		if (targetRealActivityInstance.getActivityType()
			.equals(BpmnXMLConstants.ELEMENT_EVENT_START)) {
			process.setProcessInstanceId(task.getProcessInstanceId());
			this.terminateProcess(process);
		} else {
			if (StringUtil.isNoneBlank(comment)) { // 增加评论
				taskService.addComment(taskId, task.getProcessInstanceId(),
					WfProcessConstant.COMMENT_TYPE_ROLLBACK, comment);
			}
			List<AttachmentEntityImpl> attachment = process.getAttachment();
			if (ObjectUtil.isNotEmpty(attachment)) { // 增加评论附件
				identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
				attachment.forEach(att -> taskService.saveAttachment(
					taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_ROLLBACK, taskId,
						task.getProcessInstanceId(), att.getName(), comment, att.getUrl())));
			}
			taskService.setVariable(taskId, WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE,
				WfProcessConstant.STATUS_REJECT); // 添加驳回标记

			BpmnModel model = repositoryService.getBpmnModel(task.getProcessDefinitionId());

			// 被驳回的节点是否配置了 重新提交回到驳回人
			if (model != null) {
				String backToRejecter = WfModelUtil.getUserTaskExtensionAttribute(
					process.getNodeId(),
					model, WfExtendConstant.BACK_TO_REJECTER);
				if (StringUtil.isNotBlank(backToRejecter) && "true".equals(backToRejecter)) {
					taskService.setVariable(taskId, WfExtendConstant.BACK_TO_REJECTER,
						task.getTaskDefinitionKey());
				}
			}

//			this.dispatchTaskTo(task.getProcessInstanceId(), nodeId);

			UserTask userTask = WfModelUtil.getUserTaskByKey(task.getTaskDefinitionKey(), model);

			boolean isParallelGateway = false;
			if (userTask != null) {
				List<SequenceFlow> incomingFlows = userTask.getIncomingFlows();
				for (SequenceFlow sequenceFlow : incomingFlows) {
					FlowElement element = sequenceFlow.getSourceFlowElement();
					if (element instanceof ParallelGateway || element instanceof InclusiveGateway) {
						isParallelGateway = true;
						break;
					}
					if (Objects.nonNull(element)) {
						if (element.getId().contains("Inclusive_")) {
							isParallelGateway = true;
							break;
						}
					}
				}
			}
			if (isParallelGateway) {
				this.dispatchTaskTo(task.getProcessInstanceId(), nodeId);
			} else {
				runtimeService.createChangeActivityStateBuilder()
					.processInstanceId(task.getProcessInstanceId())
					.moveActivityIdTo(task.getTaskDefinitionKey(), nodeId).changeState();
			}

			// 处理消息
			wfNoticeService.resolveNoticeInfo(new WfNotice()
				.setFromUserId(WfTaskUtil.getTaskUser())
				.setProcessId(task.getProcessInstanceId())
				.setTaskId(taskId)
				.setComment(comment)
				.setType(WfNotice.Type.REJECT));
		}

		return R.success("退回成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object terminateProcess(WfProcess process) {
		String taskId = process.getTaskId();
		String comment = process.getComment();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		BpmnModel model = repositoryService.getBpmnModel(task.getProcessDefinitionId());
		EndEvent endEvent = WfModelUtil.getEndEvent(model);
		if (endEvent == null) {
			return R.fail("流程缺少结束节点");
		}
		if (StringUtil.isBlank(task.getAssignee())) {
			taskService.claim(taskId, WfTaskUtil.getTaskUser());
		}
		// 添加终止标记
		taskService.setVariable(taskId, WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "true");
		// 增加评论
		if (StringUtil.isNoneBlank(task.getProcessInstanceId(), comment)) {
			taskService.addComment(taskId, task.getProcessInstanceId(),
				WfProcessConstant.COMMENT_TYPE_TERMINATE, comment);
		}
		// 评论附件
		List<AttachmentEntityImpl> attachment = process.getAttachment();
		if (ObjectUtil.isNotEmpty(attachment)) {
			identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
			attachment.forEach(att -> taskService.saveAttachment(
				taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_TERMINATE, taskId,
					task.getProcessInstanceId(), att.getName(), comment, att.getUrl())));
		}
		this.dispatchTaskTo(task.getProcessInstanceId(), endEvent.getId());

		// 处理消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(WfTaskUtil.getTaskUser())
			.setProcessId(task.getProcessInstanceId())
			.setComment(comment)
			.setType(WfNotice.Type.TERMINATE));

		return R.success("终止成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object addMultiInstance(WfProcess process) {
		String taskId = process.getTaskId();
		String comment =
			StringUtil.isBlank(process.getComment()) ? "" : process.getComment() + " - ";

		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		String assignee = process.getAssignee();
		String[] ids = assignee.split(",");
		List<String> usernames = new ArrayList<>();
		for (String id : ids) {
			User user = UserCache.getUser(Long.valueOf(id));
			if (user == null) {
				continue;
			}

			// 执行加签
			runtimeService.addMultiInstanceExecution(task.getTaskDefinitionKey(),
				task.getProcessInstanceId(), Collections.singletonMap("assignee", id));

			usernames.add(user.getName());
			// 处理消息
			wfNoticeService.resolveNoticeInfo(new WfNotice()
				.setFromUserId(WfTaskUtil.getTaskUser())
				.setToUserId(id)
				.setProcessId(task.getProcessInstanceId())
				.setTaskId(taskId)
				.setComment(comment)
				.setType(WfNotice.Type.ADD_MULTI_INSTANCE));
		}
		// 增加评论
		if (StringUtil.isBlank(comment) || comment.contains("管理员操作：")) {
			comment += Func.join(usernames);
			taskService.addComment(taskId, task.getProcessInstanceId(),
				WfProcessConstant.COMMENT_TYPE_ADD_MULTI_INSTANCE, comment);
		}
		// 评论附件
		List<AttachmentEntityImpl> attachment = process.getAttachment();
		if (ObjectUtil.isNotEmpty(attachment)) {
			identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
			String finalComment = comment;
			attachment.forEach(att -> taskService.saveAttachment(
				taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_ADD_MULTI_INSTANCE,
					taskId,
					task.getProcessInstanceId(), att.getName(), finalComment, att.getUrl())));
		}
		return R.success("操作成功");
	}

	@Override
	@RedisLock(value = "WfTaskLock", param = "#process.taskId")
	public Object withdrawTask(WfProcess process) {
		String taskId = process.getTaskId();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		String currentUser = WfTaskUtil.getTaskUser();
		if (task == null) {
			return R.fail("查询不到此任务");
		}
		Boolean isRevocable = this.isReturnable(taskId, currentUser);
		if (!isRevocable) {
			return R.fail("此任务不可撤回");
		}
		BpmnModel model = repositoryService.getBpmnModel(task.getProcessDefinitionId());
		if (WfProcessConstant.WITHDRAW_TYPE_START.equals(process.getWithdrawType())) { // 重新提交
			StartEvent startEvent = WfModelUtil.getStartEvent(model);
			if (startEvent != null) {
				List<SequenceFlow> outgoingFlows = startEvent.getOutgoingFlows();
				for (SequenceFlow outgoingFlow : outgoingFlows) {
					FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
					if (targetFlowElement instanceof UserTask) {
						UserTask userTask = (UserTask) targetFlowElement;
						WfTaskUser taskUser = this.getTaskUser(task.getProcessDefinitionId(),
							task.getProcessInstanceId(), userTask.getId());
						List<User> userList = taskUser.getUserList();
						if (userList.size() == 1 && currentUser.equals(
							userList.get(0).getId() + "")) {
							// 添加撤回重新提交标记
							taskService.setVariable(taskId,
								WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE,
								WfProcessConstant.STATUS_RECALL);
							// 增加评论
							taskService.addComment(taskId, task.getProcessInstanceId(),
								WfProcessConstant.COMMENT_TYPE_RECALL, "撤回重新提交");
							this.dispatchTaskTo(task.getProcessInstanceId(), userTask.getId());
							return R.success("撤回成功");
						}
					}
				}
			}
		}
		EndEvent endEvent = WfModelUtil.getEndEvent(model);
		if (endEvent == null) {
			return R.fail("流程缺少结束节点");
		}
		// 添加撤回终止标记
		taskService.setVariable(taskId, WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE,
			WfProcessConstant.STATUS_WITHDRAW);
		// 增加评论
		taskService.addComment(taskId, task.getProcessInstanceId(),
			WfProcessConstant.COMMENT_TYPE_WITHDRAW, "撤销流程");
		this.dispatchTaskTo(task.getProcessInstanceId(), endEvent.getId());
		// 处理消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(WfTaskUtil.getTaskUser())
			.setProcessId(task.getProcessInstanceId())
			.setComment("撤销流程")
			.setType(WfNotice.Type.CANCEL));

		return R.success("撤销成功");
	}

	@Override
	public Boolean isReturnable(String taskId, String currentUser) {
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if (task == null) {
			return false;
		}
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
			.processInstanceId(task.getProcessInstanceId()).singleResult();
		if (processInstance == null) {
			return false;
		}
		if (!currentUser.equals(processInstance.getStartUserId())) { // 流程发起人才可撤回
			return false;
		}
		HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
			.processInstanceId(task.getProcessInstanceId())
			.taskWithoutDeleteReason()
			.finished();

		// ======start======只允许撤回一次，若需无限撤回可注释此段代码======
//    long count = query.count();
//    if (count > 1) {
//      return false;
//    }
		// ======end======

		List<HistoricTaskInstance> list = query.list();
		for (HistoricTaskInstance t : list) { // 判断已完成的任务是否都是当前登录人处理的，若是则可撤回
			if (StringUtil.isNotBlank(t.getAssignee())) {
				if (!currentUser.equals(t.getAssignee()) && !currentUser.equals(
					processInstance.getStartUserId())) {
					return false;
				}
			} else {
				WfTaskUser taskUser = this.getTaskUser(t.getProcessDefinitionId(),
					t.getProcessInstanceId(),
					t.getTaskDefinitionKey());
				List<User> userList = taskUser.getUserList();
				if (userList.size() != 1) { // 无处理人/不是唯一处理人
					return false;
				}
				if (!currentUser.equals(userList.get(0).getId() + "")) {
					return false;
				}
			}
		}

		if (!list.isEmpty()) {
			//如果在节点上配置了不可撤回
			Future<List<WfButton>> buttonFuture = buttonService.getButtonByTaskId(
				list.get(list.size() - 1).getId());

			try {
				List<WfButton> wfButtons = buttonFuture.get();
				if (wfButtons.stream()
					.noneMatch(wfButton -> Objects.equals(wfButton.getButtonKey(), "wf_withdraw"))) {
					return false;
				}

			} catch (InterruptedException | ExecutionException e) {
				throw new RuntimeException(e);
			}

		}
		return true;
	}

	@Override
	public Boolean isMultiInstance(String taskKey, String processDefId) {
		boolean isMultiInstance = false;

		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
		Process process = bpmnModel.getMainProcess();
		FlowElement flowElement = process.getFlowElement(taskKey, true);
		if (flowElement instanceof UserTask) {
			UserTask userTask = (UserTask) flowElement;
			if (userTask.getBehavior() instanceof ParallelMultiInstanceBehavior) {
				ParallelMultiInstanceBehavior behavior = (ParallelMultiInstanceBehavior) userTask.getBehavior();
				if (behavior != null && behavior.getCollectionExpression() != null) {
					isMultiInstance = true;
				}
			}
		}
		return isMultiInstance;
	}

	@Override
	public WfTaskUser getTaskUser(String processDefId, @Nullable String processInsId,
								  String nodeId) {
		WfTaskUser taskUser = new WfTaskUser();

		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
		if (bpmnModel == null) {
			return taskUser;
		}

		List<ExtensionElement> elements = WfModelUtil.getUserTaskExtensionElements(nodeId,
			bpmnModel,
			WfExtendConstant.ASSIGNEE);
		if (elements == null) {
			elements = WfModelUtil.getSubProcessExtensionElements(nodeId, bpmnModel,
				WfExtendConstant.ASSIGNEE); // 子流程
			if (elements == null) {
				return taskUser;
			}
		}

		// 人员 - 多个 请赋值candidateUserIds，表示多个候选人
		// 角色/部门/职位 请赋值candidateGroupIds，表示多个候选组
		// 人员 - 单个 请赋值assignee，表示审核人唯一。！！赋值assignee后，候选组和候选人将失效！！
		// userList所有用户，包含组查询出来的人员和配置的人员，可用于多实例、流程图显示未到达节点的候选人。
		// Q：为什么需要配置两遍（配置了组同时又要配置人员）？ A：普通节点需要配置组，多实例节点只能配置人。为了两个方法通用，所以需要配置两遍。
		LinkedHashSet<User> userList = new LinkedHashSet<>(); // 所有用户，包含角色、部门、岗位查询出的用户，审核人等。
		LinkedHashSet<String> candidateUserIds = new LinkedHashSet<>(); // 候选用户集合
		LinkedHashSet<String> candidateGroupIds = new LinkedHashSet<>(); // 候选组集合
		String assignee = null; // 唯一审核人

		for (ExtensionElement element : elements) {
			String type = element.getAttributes().get("type").get(0).getValue();
			String value = element.getAttributes().get("value").get(0).getValue();

			switch (type) {
				case "role": // 角色
					candidateGroupIds.addAll(Func.toStrList(value));
					userList.addAll(userSearchService.listByRole(Func.toLongList(value)));
					break;
				case "dept": // 部门
					candidateGroupIds.addAll(Func.toStrList(value));
					userList.addAll(userSearchService.listByDept(Func.toLongList(value)));
					break;
				case "post": // 岗位
					candidateGroupIds.addAll(Func.toStrList(value));
					userList.addAll(userSearchService.listByPost(Func.toLongList(value)));
					break;
				case "user": // 用户
					candidateUserIds.addAll(Func.toStrList(value));
					userList.addAll(userSearchService.listByUser(Func.toLongList(value)));
					break;
				case "custom": // 自定义
					if (StringUtil.isBlank(processInsId)) {
						break;
					}
					//对于自定义的函数，如果是函数的，进行单独处理
					FunctionInfoDTO functionInfoDTO = RegExUtil.processExpression(value);
					if (Objects.nonNull(functionInfoDTO)) {
						try {
							Method method = userSearchService.getClass()
								.getDeclaredMethod(functionInfoDTO.getFunctionName(), Long.class,
									String.class, String.class);

							String paramValue = functionInfoDTO.getParamValue();
							if (Objects.nonNull(paramValue)) {
								String[] splitValueArray = paramValue.split(",");
								paramValue = Stream.of(splitValueArray).map(item -> {
									if (item.startsWith("${") && item.endsWith("}")) {
										return (String) wfExpressionService.getValue(processInsId,
											item);
									}
									return item;
								}).collect(Collectors.joining(","));
							}
							List<User> result = (List<User>) method.invoke(userSearchService,
								Long.valueOf(WfTaskUtil.getTaskUser()),
								WfTaskUtil.getTaskUserDeptId(), paramValue);

							if (Objects.nonNull(result) && !result.isEmpty()) {
								userList.addAll(result);
							}

						} catch (NoSuchMethodException | IllegalAccessException |
								 InvocationTargetException e) {
							throw new RuntimeException(e);
						}

						break;
					}
					switch (value) {
						case "currentUser": // 当前操作人
							userList.addAll(
								userSearchService.listByUser(
									Func.toLongList(WfTaskUtil.getTaskUser())));
							assignee = WfTaskUtil.getTaskUser();
							break;
						case "${assignee}":
							assignee = "${assignee}";
							break;
						case "leader":
							List<User> leaders = userSearchService.deptLeaderListByUser(
								Long.valueOf(WfTaskUtil.getTaskUser()));
							if (Objects.nonNull(leaders) && !leaders.isEmpty()) {
								userList.addAll(leaders);
							}
							break;
						case "recursionLeader":
							List<User> recursionLeaders = userSearchService.recursionGetLeaderListByDeptId(
								Long.valueOf(WfTaskUtil.getTaskUser()),
								WfTaskUtil.getTaskUserDeptId());
							if (Objects.nonNull(recursionLeaders) && !recursionLeaders.isEmpty()) {
								userList.addAll(recursionLeaders);
							}
							break;
						default:
							List<User> users = new ArrayList<>();
							if ((!value.startsWith("${") && !value.startsWith("#{")
								&& !value.endsWith("}"))) {
								value = String.format("${%s}", value);
							}
							try {
								// flowable表达式解析
								Object defaultVal = wfExpressionService.getValue(processInsId,
									value);
								if (defaultVal != null) {
									// id1,id2,id3,...
									if (defaultVal instanceof String) {
										users = userSearchService.listByUser(
											Func.toLongList(defaultVal.toString()));
									}
									// [User1, User2, User3, ...]
									else if (defaultVal instanceof List
										&& !((List) defaultVal).isEmpty()
										&& ((List) defaultVal).get(0) instanceof User) {
										users = JSON.parseArray(JSON.toJSONString(defaultVal),
											User.class);
									}
									// String [id1, id2, id3, ...]
									else if (defaultVal instanceof List
										&& !((List) defaultVal).isEmpty()
										&& ((List) defaultVal).get(0) instanceof String) {
										List<String> ids = JSON.parseArray(
											JSON.toJSONString(defaultVal), String.class);
										users = userSearchService.listByUser(
											Func.toLongList(Func.join(ids)));
									}
									// Long [id1, id2, id3, ...]
									else if (defaultVal instanceof List
										&& !((List) defaultVal).isEmpty()
										&& ((List) defaultVal).get(0) instanceof Long) {
										List<Long> ids = JSON.parseArray(
											JSON.toJSONString(defaultVal), Long.class);
										users = userSearchService.listByUser(ids);
									}
								}
							} catch (Exception ignore) {
							}
							if (ObjectUtil.isNotEmpty(users) && !users.isEmpty()) {
								userList.addAll(users);
								candidateUserIds.addAll(
									users.stream().map(u -> String.valueOf(u.getId()))
										.collect(Collectors.toSet()));
							}

							break;
					}
					break;
			}
		}
		// 替换代理人员
		userList = wfProxyService.getProxyUsers(userList, processInsId);
		userList = userList.stream().filter(Objects::nonNull)
			.collect(Collectors.toCollection(LinkedHashSet::new));
		candidateUserIds.addAll(
			userList.stream().map(u -> u.getId() + "").collect(Collectors.toSet()));
		assignee = wfProxyService.getProxyUser(assignee, processInsId);

		taskUser.setUserList(new ArrayList<>(userList));
		taskUser.setAssignee(assignee);
		taskUser.setCandidateUserIds(candidateUserIds);
		taskUser.setCandidateGroupIds(candidateGroupIds);

		return taskUser;
	}

	@Override
	public void dispatchTaskTo(String processInsId, String nodeId) {
		List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInsId)
			.list();
		List<String> executionIds = new ArrayList<>();
		executions.forEach(execution -> executionIds.add(execution.getId()));
		runtimeService.createChangeActivityStateBuilder()
			.moveExecutionsToSingleActivityId(executionIds, nodeId).changeState();
	}

	@Override
	public void setProcessStatus(WfProcess process, HistoricProcessInstance processInstance) {
		if (processInstance.getEndTime() == null) {
			process.setProcessIsFinished(WfProcessConstant.STATUS_UNFINISHED);
		} else {
			process.setProcessIsFinished(WfProcessConstant.STATUS_FINISHED);
		}
		if (processInstance.getDeleteReason() == null) { // 未逻辑删除
			HistoricVariableInstanceQuery variableInstanceQuery = historyService.createHistoricVariableInstanceQuery()
				.processInstanceId(processInstance.getId());
			HistoricVariableInstance terminate = variableInstanceQuery.variableName(
				WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE).singleResult();
			if (ObjectUtil.isNotEmpty(terminate) && ObjectUtil.isNotEmpty(terminate.getValue())) {
				String processIsFinished = terminate.getValue().toString();
				if ("true".equals(processIsFinished)) {
					processIsFinished = WfProcessConstant.STATUS_TERMINATE;
				}
				process.setProcessIsFinished(processIsFinished);
			}
		} else {
			process.setProcessIsFinished(WfProcessConstant.STATUS_DELETED);
		}
	}

	@Override
	public long todoCount() {
		String taskUser = WfTaskUtil.getTaskUser();
		String taskGroup = WfTaskUtil.getCandidateGroup();
		TaskQuery taskQuery = taskService.createTaskQuery()
			.orderByTaskCreateTime()
			.desc()
			.taskTenantId(WfTaskUtil.getTenantId())
			.active()
			.taskCandidateOrAssigned(taskUser)
			.taskCandidateGroupIn(Func.toStrList(taskGroup));
		WfProcess process = new WfProcess();
		WfSearchUtil.buildSearchQuery(taskQuery, process); // 搜索条件
		return taskQuery.count();
	}

	@Override
	public WfProcess syncDetail(String taskId, String assignee, String candidateGroup) {
		return getDetail(taskId, assignee, candidateGroup);
	}

	/**
	 * @param processInstanceId
	 * @param data
	 */
	@Override
	public void addVariable(String processInstanceId, Map<String, Object> data) {
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
			.processInstanceId(processInstanceId).singleResult();
		if (processInstance != null) {
			Map<String, Object> variables = processInstance.getProcessVariables();
			variables.putAll(data);
			runtimeService.setVariables(processInstanceId, variables);
		}
	}

	/**
	 * 审核通过
	 */
	private void passTask(WfProcess process, Task task) {
		String taskId = task.getId();
		String processInstanceId = process.getProcessInstanceId();
		String processDefinitionId = process.getProcessDefinitionId();
		String assignee = process.getAssignee();
		String comment = process.getComment();

		// 创建变量
		Map<String, Object> variables = process.getVariables();
		if (variables == null) {
			variables = new HashMap<>();
		}
//		variables.put(WfProcessConstant.PASS_KEY, process.isPass());

		if (StringUtil.isNoneBlank(processInstanceId, comment)) { // 增加评论
			taskService.addComment(taskId, processInstanceId, comment);
		}
		List<AttachmentEntityImpl> attachment = process.getAttachment();
		if (ObjectUtil.isNotEmpty(attachment)) { // 增加评论附件
			identityService.setAuthenticatedUserId(WfTaskUtil.getTaskUser());
			String finalComment = comment;
			attachment.forEach(att -> taskService.saveAttachment(
				taskService.createAttachment(WfProcessConstant.COMMENT_TYPE_COMMENT, taskId,
					task.getProcessInstanceId(), att.getName(), finalComment, att.getUrl())));
		}

		taskService.removeVariable(taskId,
			WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE); // 删除撤回/驳回标记

		boolean needComplete = true;
		// 重新提交回到驳回人
		Object nodeId = runtimeService.getVariable(processInstanceId,
			WfExtendConstant.BACK_TO_REJECTER);
		if (ObjectUtil.isNotEmpty(nodeId) && StringUtil.isNotBlank(processDefinitionId)) {
			BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
			String backToRejecter = WfModelUtil.getUserTaskExtensionAttribute(
				task.getTaskDefinitionKey(),
				bpmnModel, WfExtendConstant.BACK_TO_REJECTER);
			if (StringUtil.isNotBlank(backToRejecter) && "true".equals(backToRejecter)) {
				taskService.setVariables(taskId, variables);
				this.dispatchTaskTo(processInstanceId, nodeId.toString());
				runtimeService.removeVariable(processInstanceId, WfExtendConstant.BACK_TO_REJECTER);
				needComplete = false;
			}
		}

		if (needComplete) {
			if (StringUtil.isNotBlank(task.getOwner())) { // 转办/委托设置了owner
				DelegationState delegationState = task.getDelegationState();
				if (delegationState != null) {
					switch (delegationState) {
						case PENDING: // 委托任务先处理，处理完成后会回到委派人的任务中，再执行完成
							if (StringUtil.isEmpty(comment)) { // 默认委托处理意见，防止被委托人处理完后委托人无法理解
								comment = WfTaskUtil.getNickName() + "：同意";
								taskService.addComment(taskId, processInstanceId, comment);
							}
							taskService.resolveTask(taskId, variables);
//						taskService.complete(taskId, variables);
							break;
						case RESOLVED: // 已处理委托
						default: // 无委托
							taskService.complete(taskId, variables);
							break;
					}
				} else {
					taskService.complete(taskId, variables);
				}
			} else if (StringUtil.isEmpty(task.getAssignee())) { // 待签任务，先签收
				this.claimTask(taskId); //当这个任务有多个人可以执行的时候，使用此函数后，表明由当前审核人负责执行这个任务
				taskService.complete(taskId, variables);
				this.autoPassTask(processInstanceId, task.getAssignee(), process);
			} else { //审核通过
				taskService.complete(taskId, variables);
				this.autoPassTask(processInstanceId, task.getAssignee(), process);
			}
		}

		// 指定下一步审批人
		this.handleNextNodeAssignee(processInstanceId, assignee);

		// 处理消息
		wfNoticeService.resolveNoticeInfo(new WfNotice()
			.setFromUserId(WfTaskUtil.getTaskUser())
			.setProcessId(processInstanceId)
			.setTaskId(taskId)
			.setComment(comment)
			.setTaskVariables(variables)
			.setType(WfNotice.Type.PASS));
	}

	/**
	 * 审核不通过
	 */
	private void rejectTask(WfProcess process) {
		String taskKey = process.getTaskDefinitionKey();
		String processDefinitionId = process.getProcessDefinitionId();
		String rollbackNode = null;

		if (StringUtil.isNotBlank(processDefinitionId)) {
			BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

			// 判断节点上是否配置了驳回节点，若没有配置则使用流程上配置的驳回节点，若流程也没有配置则驳回到上一节点
			List<ExtensionAttribute> attributes;
			rollbackNode = WfModelUtil.getUserTaskExtensionAttribute(taskKey, bpmnModel,
				WfExtendConstant.ROLLBACK_NODE);
			if (StringUtil.isBlank(rollbackNode)) {
				attributes = WfModelUtil.getProcessExtensionAttributes(bpmnModel,
					WfExtendConstant.ROLLBACK_NODE);
				if (attributes != null) {
					rollbackNode = attributes.get(0).getValue();
				}
			}
		}
		List<WfNode> backNodes = this.getBackNodes(process);
		if (!backNodes.isEmpty()) {
			String finalRollbackNode = rollbackNode;
			// 配置了默认退回节点并且可退回节点中包含配置的退回节点
			if (StringUtil.isNotBlank(rollbackNode) &&
				backNodes.stream().filter(wfNode -> wfNode.getNodeId().equals(finalRollbackNode))
					.findAny().orElse(null) != null) {
				process.setNodeId(rollbackNode);
			} else {
				WfNode node = backNodes.get(backNodes.size() - 1);
				process.setNodeId(node.getNodeId());
			}
			this.rollbackTask(process);
		}
	}

	/**
	 * 构建流程列表
	 */
	private void buildProcessList(List<WfProcess> wfProcessList,
								  HistoricProcessInstanceQuery historyQuery, Query query) {
		// 查询列表
		List<HistoricProcessInstance> historyList;
		if (query == null || query.getSize() == -1) {
			historyList = historyQuery.list();
		} else {
			historyList = historyQuery.listPage(
				Func.toInt((query.getCurrent() - 1) * query.getSize()),
				Func.toInt(query.getSize()));
		}

		historyList.forEach(historicProcessInstance -> {
			WfProcess process = new WfProcess();
			// historicProcessInstance
			process.setCreateTime(historicProcessInstance.getStartTime());
			process.setEndTime(historicProcessInstance.getEndTime());
			process.setProcessInstanceId(historicProcessInstance.getId());
			// Variables
			process.setVariables(
				buildTaskVariables(WfProcessConstant.STATUS_DONE, historicProcessInstance.getId(),
					process));
			// ProcessDefinition
			FlowProcess processDefinition = WfProcessCache.getProcessDefinition(
				historicProcessInstance.getProcessDefinitionId());
			if (processDefinition != null) {
				process.setProcessDefinitionId(processDefinition.getId());
				process.setProcessDefinitionName(processDefinition.getName());
				process.setProcessDefinitionKey(processDefinition.getKey());
				process.setCategory(processDefinition.getCategory());
			}
			// HistoricTaskInstance
			List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery()
				.processInstanceId(historicProcessInstance.getId())
				.orderByTaskCreateTime().desc()
				.orderByHistoricTaskInstanceEndTime().desc()
				.list();
			if (Func.isNotEmpty(historyTasks)) {
				List<HistoricTaskInstance> runTasks = historyTasks.stream()
					.filter(task -> task.getEndTime() == null).collect(Collectors.toList());
				HistoricTaskInstance historyTask = null;
				if (runTasks.isEmpty()) {
					historyTask = historyTasks.iterator().next();
				} else {
					historyTask = runTasks.iterator().next();
				}
				process.setTaskId(historyTask.getId());
				process.setTaskName(historyTask.getName());

				// ExForm
				buildExForm(historyTask.getProcessDefinitionId(),
					historyTask.getTaskDefinitionKey(),
					process);
			}
			// Status
			if (historicProcessInstance.getEndActivityId() != null) {
				process.setTaskName("结束");
			}
			this.setProcessStatus(process, historicProcessInstance);
			wfProcessList.add(process);
		});
	}

	/**
	 * 构建任务列表
	 */
	@SuppressWarnings("unchecked")
	private void buildTaskList(List<WfProcess> wfProcessList,
							   org.flowable.common.engine.api.query.Query taskQuery, Query query, String status) {
		List taskList = taskQuery.listPage(Func.toInt((query.getCurrent() - 1) * query.getSize()),
			Func.toInt(query.getSize()));

		taskList.forEach(task -> {
			HistoricTaskInstance t1 = null;
			Task t2 = null;
			if (task instanceof HistoricTaskInstance) {
				t1 = (HistoricTaskInstance) task;
			} else {
				t2 = (Task) task;
			}
			String taskId = t1 == null ? t2.getId() : t1.getId(); // 任务id
			String taskDefKey =
				t1 == null ? t2.getTaskDefinitionKey() : t1.getTaskDefinitionKey(); // 任务key
			String taskName = t1 == null ? t2.getName() : t1.getName(); // 任务名称
			Date taskCreateTime = t1 == null ? t2.getCreateTime() : t1.getCreateTime(); // 任务创建时间
			String processInsId =
				t1 == null ? t2.getProcessInstanceId() : t1.getProcessInstanceId(); // 流程实例id
			String processDefId =
				t1 == null ? t2.getProcessDefinitionId() : t1.getProcessDefinitionId(); // 流程定义id

			WfProcess process = new WfProcess();
			process.setTaskId(taskId);
			process.setTaskDefinitionKey(taskDefKey);
			process.setTaskName(taskName);
			process.setCreateTime(taskCreateTime);
			process.setStatus(status);
			process.setProcessInstanceId(processInsId);
			// Variables
			process.setVariables(buildTaskVariables(status, processInsId, process));
			// ProcessDefinition
			FlowProcess processDefinition = WfProcessCache.getProcessDefinition(processDefId);
			if (processDefinition != null) {
				process.setCategory(processDefinition.getCategory());
				process.setProcessDefinitionId(processDefinition.getId());
				process.setProcessDefinitionName(processDefinition.getName());
				process.setProcessDefinitionKey(processDefinition.getKey());
			}
			// ExForm
			buildExForm(processDefId, taskDefKey, process);

			wfProcessList.add(process);
		});
	}

	/**
	 * 构建业务变量
	 */
	private Map<String, Object> buildTaskVariables(String status, String processInsId,
												   WfProcess process) {
		Map<String, Object> variables = new HashMap<>();
		if (WfProcessConstant.STATUS_TODO.equals(status)) { // 进行中实例
			Object applyUsername = runtimeService.getVariable(processInsId,
				WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME);
			// 发起人
			if (ObjectUtil.isNotEmpty(applyUsername)) {
				variables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME,
					applyUsername.toString());
				process.setStartUsername(applyUsername.toString());
			}
			// 流水号
			Object sn = runtimeService.getVariable(processInsId,
				WfProcessConstant.TASK_VARIABLE_SN);
			if (ObjectUtil.isNotEmpty(sn)) {
				variables.put(WfProcessConstant.TASK_VARIABLE_SN, sn.toString());
			}
			Object serialNo = runtimeService.getVariable(processInsId,
				"serialNo");
			if (ObjectUtil.isNotEmpty(serialNo)) {
				variables.put("serialNo", serialNo.toString());
			}
		} else { // 历史实例
			HistoricVariableInstanceQuery variableInstanceQuery = historyService.createHistoricVariableInstanceQuery()
				.processInstanceId(processInsId);
			// 发起人
			HistoricVariableInstance applyUsername = variableInstanceQuery.variableName(
				WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME).singleResult();
			if (ObjectUtil.isNotEmpty(applyUsername)) {
				variables.put(WfProcessConstant.TASK_VARIABLE_APPLY_USER_NAME,
					applyUsername.getValue());
				process.setStartUsername(applyUsername.getValue().toString());
			}
			// 流水号
			HistoricVariableInstance sn = variableInstanceQuery.variableName(
				WfProcessConstant.TASK_VARIABLE_SN).singleResult();
			if (ObjectUtil.isNotEmpty(sn)) {
				variables.put(WfProcessConstant.TASK_VARIABLE_SN, sn.getValue());
			}
			HistoricVariableInstance serialNo = variableInstanceQuery.variableName(
				"serialNo").singleResult();
			if (ObjectUtil.isNotEmpty(serialNo)) {
				variables.put("serialNo", serialNo.getValue());
			}
		}

		return variables;
	}

	/**
	 * 构建外置表单url
	 */
	private void buildExForm(String processDefId, String taskDefKey, WfProcess process) {
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
		String exFormKey = WfModelUtil.getUserTaskExtensionAttribute(taskDefKey, bpmnModel,
			WfExtendConstant.EX_FORM_KEY);
		if (StringUtil.isNotBlank(exFormKey)) {
			process.setFormKey(WfProcessConstant.EX_FORM_PREFIX + exFormKey);
		}

		String exFormUrl = WfModelUtil.getUserTaskExtensionAttribute(taskDefKey, bpmnModel,
			WfExtendConstant.EX_FORM_URL);
		if (StringUtil.isNotBlank(exFormUrl)) {
			process.setFormUrl(exFormUrl);
		}

		String exAppFormUrl = WfModelUtil.getUserTaskExtensionAttribute(taskDefKey, bpmnModel,
			WfExtendConstant.EX_APP_FORM_URL);
		if (StringUtil.isNotBlank(exAppFormUrl)) {
			process.setAppFormUrl(exAppFormUrl);
		}
	}

	/**
	 * 处理指定下一步审核人
	 *
	 * @param processInsId 流程实例id
	 * @param assignee     下一步审核人
	 */
	@Async
	void handleNextNodeAssignee(String processInsId, Object assignee) {
		List<Task> list = taskService.createTaskQuery()
			.processInstanceId(processInsId)
			.taskVariableNotExists(WfProcessConstant.TASK_VARIABLE_APPOINT)
			.list();
		if (list.isEmpty()) {
			return;
		}
		if (ObjectUtil.isNotEmpty(assignee)) {
			String[] ids = assignee.toString().split(",");
			if (list.size() > 1 || (list.size() == 1 && this.isMultiInstance(
				list.get(0).getTaskDefinitionKey(), list.get(0).getProcessDefinitionId()))) { // 多实例
				int index = 0;
				if (list.size() == ids.length) { // 实例数量等于指定数量，循环赋值
					for (Task task : list) {
						taskService.setAssignee(task.getId(), ids[index]);
						taskService.setVariableLocal(task.getId(),
							WfProcessConstant.TASK_VARIABLE_APPOINT,
							"1");
						index++;
					}
				} else if (list.size() > ids.length) { // 实例数量大于指定数量，需动态减签
					for (int i = 0; i < ids.length; i++) {
						taskService.setAssignee(list.get(i).getId(), ids[i]);
						taskService.setVariableLocal(list.get(i).getId(),
							WfProcessConstant.TASK_VARIABLE_APPOINT, "1");
					}
					list.subList(ids.length, list.size()).forEach(
						task -> runtimeService.deleteMultiInstanceExecution(task.getExecutionId(),
							false));
				} else { // 实例数量小于指定数量，需动态加签
					for (int i = 0; i < list.size(); i++) {
						taskService.setAssignee(list.get(i).getId(), ids[i]);
						taskService.setVariableLocal(list.get(i).getId(),
							WfProcessConstant.TASK_VARIABLE_APPOINT, "1");
					}
					for (int i = 0; i < ids.length - list.size(); i++) {
						Task task = list.get(0);
						runtimeService.addMultiInstanceExecution(task.getTaskDefinitionKey(),
							task.getProcessInstanceId(),
							Collections.singletonMap("assignee", ids[i + list.size()]));
					}
					list = taskService.createTaskQuery()
						.processInstanceId(processInsId)
						.taskVariableNotExists(WfProcessConstant.TASK_VARIABLE_APPOINT)
						.list();
					list.forEach(
						t -> taskService.setVariableLocal(t.getId(),
							WfProcessConstant.TASK_VARIABLE_APPOINT,
							"1"));
				}
			} else {
				taskService.setAssignee(list.get(0).getId(), ids[0]);
				taskService.setVariableLocal(list.get(0).getId(),
					WfProcessConstant.TASK_VARIABLE_APPOINT,
					"1");
			}
		} else {
			for (Task task : list) {
				taskService.setVariableLocal(task.getId(), WfProcessConstant.TASK_VARIABLE_APPOINT,
					"1");
			}
		}
	}

	/**
	 * 如果当前任务和下一个任务 是同一个审核的人话，那么任务将自动通过。 前提：下一个任务节点必须执行人必须只有一个人。当前执行节点，
	 * 执行者可以是多个(如果是多个，那么当前任务必须是只要有一个通过，那么任务就通过)也可是单个
	 *
	 * @param processInstanceId 流程实例的Id
	 * @param currentUserId     当前任务的审核人
	 * @param process           流程信息
	 */
	public void autoPassTask(String processInstanceId, String currentUserId, WfProcess process) {
		List<Task> list = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
		//自动通过
		if (Objects.nonNull(list) && !list.isEmpty()) {
			Task nextTaskUser = list.get(0);
			boolean noAutoPass = nextTaskUser.getTaskDefinitionKey().contains("no_auto_pass");
			if (Objects.equals(currentUserId, nextTaskUser.getAssignee()) && !noAutoPass) {
				this.passTask(process, nextTaskUser);
			}
		}
	}

	@Async
	@Override
	public void completeProcessTask(String processInstanceId, Long copyUserId, String taskNickName,
									String taskUser) {
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
			.processInstanceId(processInstanceId).singleResult();
		List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId)
			.active().list();
		if (Objects.isNull(taskList) || taskList.isEmpty()) {
			return;
		}
		Task task = taskList.get(0);
		String taskId = task.getId();
		WfProcess wfProcess = new WfProcess();
		wfProcess.setProcessInstanceId(processInstanceId);
		wfProcess.setTaskId(taskId);
		wfProcess.setPass(true);
		wfProcess.setCopyUser(String.valueOf(copyUserId));
		taskService.setVariable(taskId, WfProcessConstant.PASS_KEY, wfProcess.isPass());
		wfProcess.setTaskName(processInstance.getProcessDefinitionName() + "-" + task.getName());
		wfProcess.setAssigneeName(WfTaskUtil.getNickName());
		wfProcess.setAssigneeName(taskNickName);
		wfProcess.setTaskDefinitionKey(task.getTaskDefinitionKey());
		runtimeService.setVariable(task.getProcessInstanceId(), "wf_latest_task_assignee",
			taskUser);
		//流程通过
		this.passTask(wfProcess, task);
		//设置抄送人
		wfProcess.setAssignee(WfTaskUtil.getTaskUser());
		wfProcess.setAssignee(taskUser);
		wfCopyService.resolveCopyUser(wfProcess);
	}

	/**
	 * @param processInsIds
	 * @return
	 */
	@Override
	public List<WfProcess> getHistoryFlowListByProcessInsIds(List<String> processInsIds) {
		List<WfProcess> flowList = new LinkedList<>();
		List<HistoricActivityInstance> historicActivityInstanceList = new ArrayList<>();
		List<Comment> commentList = new ArrayList<>();
		List<Attachment> attachmentList = new ArrayList<>();
		processInsIds.forEach(processInsId -> {
			historicActivityInstanceList.addAll(historyService.createHistoricActivityInstanceQuery()
				.processInstanceId(processInsId)
				.orderByHistoricActivityInstanceStartTime().asc()
				.orderByHistoricActivityInstanceEndTime().asc()
				.list());
			commentList.addAll(taskService.getProcessInstanceComments(processInsId));
			attachmentList.addAll(taskService.getProcessInstanceAttachments(
				processInsId));
		});
		List<HistoricProcessInstance> processInstanceList = historyService.createHistoricProcessInstanceQuery()
			.processInstanceIds(new HashSet<>(processInsIds)).orderByProcessInstanceStartTime()
			.asc().list();
		Map<String, HistoricProcessInstance> processInstanceMap = processInstanceList.stream()
			.collect(Collectors.toMap(
				item -> ((HistoricProcessInstanceEntityImpl) item).getProcessInstanceId(),
				Function.identity()));
		Map<String, Integer> activityMap = new HashMap<>(16);
		for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceList) {
			// 显示开始节点和结束节点，并且执行人不为空的任务
			if (Arrays.asList(
				WfProcessConstant.USER_TASK,
				WfProcessConstant.START_EVENT,
				WfProcessConstant.END_EVENT,
				WfProcessConstant.SEQUENCE_FLOW,
				WfProcessConstant.HTTP_SERVICE_TASK,
				WfProcessConstant.SHELL_SERVICE_TASK,
				WfProcessConstant.MAIL_SERVICE_TASK
			).contains(historicActivityInstance.getActivityType())) {
				// 给节点增加序号
				activityMap.computeIfAbsent(historicActivityInstance.getActivityId(),
					k -> activityMap.size());
				WfProcess flow = new WfProcess();
				flow.setProcessInstanceId(historicActivityInstance.getProcessInstanceId());
				flow.setHistoryActivityId(historicActivityInstance.getActivityId());
				flow.setHistoryActivityName(historicActivityInstance.getActivityName());
				flow.setHistoryActivityType(historicActivityInstance.getActivityType());
				flow.setCreateTime(historicActivityInstance.getStartTime());
				flow.setEndTime(historicActivityInstance.getEndTime());
				String durationTime = DateUtil.secondToTime(
					Func.toLong(historicActivityInstance.getDurationInMillis(), 0L) / 1000);
				flow.setHistoryActivityDurationTime(durationTime);
				// 获取流程发起人名称
				if (WfProcessConstant.START_EVENT.equals(
					historicActivityInstance.getActivityType())) {
					HistoricProcessInstance processInstance = processInstanceMap.get(
						historicActivityInstance.getProcessInstanceId());
					if (processInstance != null && StringUtil.isNotBlank(processInstance.getStartUserId())) {
						String taskUser = processInstance.getStartUserId();
						User user = UserCache.getUser(WfTaskUtil.getUserId(taskUser));
						if (user != null) {
							flow.setAssignee(historicActivityInstance.getAssignee());
							flow.setAssigneeName(user.getName());
						}
					}
				} else if (WfProcessConstant.USER_TASK.equals(
					historicActivityInstance.getActivityType())) {
					// 获取任务执行人名称
					if (StringUtil.isNotBlank(historicActivityInstance.getAssignee())) {
						User user = UserCache.getUser(
							WfTaskUtil.getUserId(historicActivityInstance.getAssignee()));
						if (user != null) {
							flow.setAssignee(historicActivityInstance.getAssignee());
							flow.setAssigneeName(user.getName());
						}
					} else {
						WfTaskUser taskUser = this.getTaskUser(
							historicActivityInstance.getProcessDefinitionId(),
							historicActivityInstance.getProcessInstanceId(),
							historicActivityInstance.getActivityId());
						List<User> userList = taskUser.getUserList();
						if (ObjectUtil.isNotEmpty(userList)) {
							if (userList.size() == 1) {
								flow.setAssignee(userList.get(0).getId() + "");
								flow.setAssigneeName(userList.get(0).getName());
							} else {
								flow.setAssigneeName(
									userList.stream().map(User::getName)
										.collect(Collectors.joining("/")));
							}
						}
					}
				}

				// 获取意见评论内容/附件
				if (StringUtil.isNotBlank(historicActivityInstance.getTaskId())) {
					List<Comment> comments = new ArrayList<>();
					for (Comment comment : commentList) {
						if (comment.getTaskId().equals(historicActivityInstance.getTaskId())) {
							comments.add(comment);
						}
					}
					flow.setComments(comments);

					List<Attachment> attachments = new ArrayList<>();
					for (Attachment attachment : attachmentList) {
						if (attachment.getTaskId().equals(historicActivityInstance.getTaskId())) {
							attachments.add(attachment);
						}
					}
					flow.setAttachments(attachments);
				}
				flowList.add(flow);
			}
		}
		// 处理未流转到的节点
		historicActivityInstanceList.forEach(item -> {
			String processDefId = item.getProcessDefinitionId();
			String processInsId = item.getProcessInstanceId();
			HistoricProcessInstance processInstance = processInstanceMap.get(processInsId);
			if (processInstance.getEndTime() == null) { // 流程结束后不再处理未流转到的节点
				BpmnModel model = repositoryService.getBpmnModel(processDefId);
				List<FlowElement> elements = new ArrayList<>();
				model.getMainProcess().getFlowElements().forEach(flowElement -> {
					if (flowElement instanceof UserTask) {
						WfProcess wfProcess = flowList.stream()
							.filter(flow -> flow.getHistoryActivityId().equals(flowElement.getId()))
							.findFirst()
							.orElse(null);
						if (wfProcess == null) {
							elements.add(flowElement);
						}
					}
				});
				if (!elements.isEmpty()) {
					elements.forEach(element -> {
						WfProcess flow = new WfProcess();
						flow.setProcessInstanceId(processInsId);
						flow.setHistoryActivityId(element.getId());
						flow.setHistoryActivityName(element.getName());
						flow.setHistoryActivityType(WfProcessConstant.CANDIDATE);
						List<User> userList = this.getTaskUser(processDefId, processInsId,
								element.getId())
							.getUserList();
						if (ObjectUtil.isNotEmpty(userList)) {
							flow.setAssigneeName(
								userList.stream().map(User::getName)
									.collect(Collectors.joining("/")));
						}
						flowList.add(flow);
					});
				}
			}
		});
		return flowList;
	}


}

