package org.springblade.modules.ws;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.websocket.HandshakeResponse;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.undertow.server.HttpServerExchange;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.util.HeaderMap;
import io.undertow.util.HttpString;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
@Slf4j
@Component
public class ServerConfigurator extends ServerEndpointConfig.Configurator {

	/**
	 * ws协议只能通过这个数据进行参数数据
	 */
	private static final String HEADER_KEY = "Sec-WebSocket-Protocol";

	private static final String HEADER_KEY_AUTHORIZATION = "Authorization";

	private static final String HEADER_KEY_BLADE_AUTH = "Blade-Auth";

	private static final String HEADER_KEY_BLADE_USER_ID = "userId";


	/**
	 * token鉴权认证
	 *
	 * @param originHeaderValue header中的数据
	 * @return 鉴权结果
	 */
	@Override
	public boolean checkOrigin(String originHeaderValue) {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (servletRequestAttributes == null) {
			return false;
		}
		HttpServletRequest request = servletRequestAttributes.getRequest();

		HttpServletResponse response = servletRequestAttributes.getResponse();

		String auth = request.getHeader(HEADER_KEY);

		Objects.requireNonNull(auth,"非法请求");

		JSONObject json;

		try {
			String headerData = URLDecoder.decode(auth, "UTF-8");

			json = JSON.parseObject(headerData);

		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		Objects.requireNonNull(response, "响应体为空");

		response.addHeader(HEADER_KEY, auth);

		try {
			if (setRequestHeader(request, json)) {
				return super.checkOrigin(originHeaderValue);
			}
			return false;
		} catch (IllegalAccessException e) {
			throw new RuntimeException(e);
		}

	}


	/**
	 * 动态反射设置reqeust的请求头
	 *
	 * @param request    request对象
	 * @param jsonObject 头信息
	 * @throws IllegalAccessException 非法访问异常
	 */
	private static boolean setRequestHeader(HttpServletRequest request, JSONObject jsonObject) throws IllegalAccessException {
		// 从HttpServletRequestImpl中获取HttpServerExchange
		Field exchangeField = ReflectionUtils.findField(HttpServletRequestImpl.class, "exchange", HttpServerExchange.class);

		Objects.requireNonNull(exchangeField, "反射失败");

		exchangeField.setAccessible(true);
		HttpServerExchange httpServerExchange = (HttpServerExchange) exchangeField.get(request);

		// 从HttpServerExchange中获取HeaderMap
		Field headerMapField = ReflectionUtils.findField(HttpServerExchange.class, "requestHeaders", HeaderMap.class);

		Objects.requireNonNull(headerMapField, "反射失败");

		headerMapField.setAccessible(true);

		HeaderMap requestHeaderMap = (HeaderMap) headerMapField.get(httpServerExchange);

		requestHeaderMap.add(new HttpString(HEADER_KEY_AUTHORIZATION), jsonObject.getString(HEADER_KEY_AUTHORIZATION));

		requestHeaderMap.add(new HttpString(HEADER_KEY_BLADE_AUTH), jsonObject.getString(HEADER_KEY_BLADE_AUTH));

		BladeUser user = AuthUtil.getUser(request);

		if (Objects.isNull(user)) {
			return false;
		}
		requestHeaderMap.add(new HttpString(HEADER_KEY_BLADE_USER_ID), user.getUserId());
		return true;
	}


	/**
	 * 通过setRequestHeader设置的userId,获取到，在websocketServer的onOpen中拿到，来保证server的唯一标识符
	 *
	 * @param sec      握手中涉及的配置对象
	 * @param request  opeding的reuqest
	 * @param response 开启握手的响应
	 */
	@Override
	public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request,
								HandshakeResponse response) {
		Map<String, List<String>> headers = request.getHeaders();

		sec.getUserProperties().put(HEADER_KEY_BLADE_USER_ID, headers.get(HEADER_KEY_BLADE_USER_ID).get(0));

		super.modifyHandshake(sec, request, response);
	}


}
