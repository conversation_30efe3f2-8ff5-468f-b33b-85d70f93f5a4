package org.springblade.modules.ws;

import java.io.IOException;
import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@ServerEndpoint(value = "/socket/message", configurator = ServerConfigurator.class)
@Slf4j
@Component
public class WebSocketServer implements Serializable {

  private static final long serialVersionUID = -2577963058693391709L;

  /**
   * 记录在线的人数
   */

  /**
   * ConcurrentHashMap记录server的实例
   */
  private static final ConcurrentHashMap<Long, WebSocketServer> WEBSOCKET_MAP = new ConcurrentHashMap<>();

  //与某个客户端的连接会话，需要通过它来给客户端发送数据
  private Session session;

  //接收sid 请求唯一标识
  private Long sid;

  /**
   * 连接建立成功调用的方法
   */
  @OnOpen
  public void onOpen(Session session, EndpointConfig config) {
    this.session = session;
    Object userId = config.getUserProperties().get("userId");
    this.sid = Long.valueOf(userId.toString());
    WEBSOCKET_MAP.put(sid, this);     //加入set中
    log.info("ws建立连接,连接用户:{},当前在线人数为:{}", sid, getOnlineCount());
    SpringUtil.publishEvent(new WebSocketOpenEvent(this.sid));
  }

  /**
   * 连接关闭调用的方法
   */
  @OnClose
  public void onClose() {
    WEBSOCKET_MAP.remove(this.sid);  //从set中删除
    log.info("ws连接断开！当前在线人数为：{}", getOnlineCount());
  }

  /**
   * 收到客户端消息后调用的方法
   *
   * @param message 客户端发送过来的消息
   */
  @OnMessage
  public void onMessage(String message) {
    sendWholeAsyncMessage(sid, message);
  }

  /**
   * @param error 错误信息
   */
  @OnError
  public void onError(Throwable error) {
    log.error("ws错误");
    error.printStackTrace();
  }

  /**
   * 当前客户端发送消息 实现服务器主动推送
   */
  public void sendMessage(String message) {
    this.session.getAsyncRemote().sendText(message);
  }

  /**
   * 指定单个用户发送 异步推送消息
   *
   * @param userId  用户id
   * @param message 消息内容
   */
  public static void sendWholeAsyncMessage(Long userId, String message) {

    WebSocketServer webSocketServer = WEBSOCKET_MAP.get(userId);
    if (Objects.nonNull(webSocketServer)) {
      webSocketServer.session.getAsyncRemote().sendText(message);
    }
  }


  /**
   * 发送给所有在线的
   */
  public static void sendAllOnline(String message) throws IOException {
    WEBSOCKET_MAP.forEach((k, v) -> v.sendMessage(message));
  }

  /**
   * 当前在线的人数
   *
   * @return 人数
   */
  public static int getOnlineCount() {
    return WEBSOCKET_MAP.size();
  }


}
