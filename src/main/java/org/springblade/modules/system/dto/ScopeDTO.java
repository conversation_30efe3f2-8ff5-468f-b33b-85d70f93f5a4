package org.springblade.modules.system.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据权限DTO
 *
 * <AUTHOR>
 */
@Data
public class ScopeDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 部门ID，多个用逗号隔开
	 */
	private String deptId;


	/**
	 * 权限分类
	 * 全部可见 1
	 * <p>
	 * 本人可见 2
	 * <p>
	 * 所在机构可见 3
	 * <p>
	 * 所在机构及子级可见 4
	 * <p>
	 * 自定义 5
	 */
	private Integer scopeType;

	/**
	 * 用户ID，多个用逗号隔开
	 */
	private String personId;

}
