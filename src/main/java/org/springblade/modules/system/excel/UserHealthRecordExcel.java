/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * RegionExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserHealthRecordExcel implements Serializable {

  private static final long serialVersionUID = 3910524994411564420L;
  @ColumnWidth(20)
  @ExcelProperty("体检日期")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date examinationDate;
  @ExcelProperty("员工")
  private String userName;
  @ExcelProperty("身高(cm)")
  private String height;

  @ExcelProperty("体重(kg)")
  private String weight;

  @ExcelProperty("血压-高压(mmHg)")
  private String bloodPressureMax;

  @ExcelProperty("血压-低压(mmHg)")
  private String bloodPressureMin;

  @ExcelProperty("血糖(mmol/L)")
  private String bloodSugar;
  @ExcelProperty("尿糖(mmol/L)")
  private String glucose;
  @ExcelProperty("甘油三酯(mmol/L)")
  private String triglycerides;

  @ExcelProperty("胆固醇(mmol/L)")
  private String cholesterol;

  @ExcelProperty("高密度脂蛋白(mmol/L)")
  private String hdl;

  @ExcelProperty("更多")
  private String more;
}
