/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * UserExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ColumnWidth(15)
	@ExcelProperty("租户编号")
	private String tenantId;

	@ExcelIgnore
	@ExcelProperty("用户平台")
	private String userType;

	@ColumnWidth(20)
	@ExcelProperty("用户平台名称")
	private String userTypeName;

	@ColumnWidth(15)
	@ExcelProperty("账户")
	private String account;

	@ExcelIgnore
	// @ColumnWidth(10)
	@ExcelProperty("密码")
	private String password;

	@ColumnWidth(13)
	@ExcelProperty("用户编号")
	private String code;

	@ColumnWidth(10)
	@ExcelProperty("昵称")
	private String name;

	@ColumnWidth(10)
	@ExcelProperty("姓名")
	private String realName;

	@ColumnWidth(10)
	@ExcelProperty("邮箱")
	private String email;

	@ColumnWidth(15)
	@ExcelProperty("手机")
	private String phone;

	@ExcelIgnore
	@ExcelProperty("角色ID")
	private String roleId;

	@ExcelIgnore
	@ExcelProperty("部门ID")
	private String deptId;

	@ExcelIgnore
	@ExcelProperty("岗位ID")
	private String postId;

	@ColumnWidth(15)
	@ExcelProperty("角色名称")
	private String roleName;

	@ColumnWidth(15)
	@ExcelProperty("部门名称")
	private String deptName;

	@ColumnWidth(15)
	@ExcelProperty("岗位名称")
	private String postName;

	@ExcelIgnore
	// @ColumnWidth(11)
	@ExcelProperty("生日")
	private Date birthday;

	@ColumnWidth(12)
	@ExcelProperty("入职时间")
	private Date entryTime;

	@ColumnWidth(19)
	@ExcelProperty("身份证号")
	private String idCard;

	@ExcelIgnore
	@ExcelProperty("是否已婚key")
	private Integer isMarried;

	@ColumnWidth(12)
	@ExcelProperty("是否已婚")
	private String isMarriedName;

	@ColumnWidth(20)
	@ExcelProperty("户籍")
	private String householdRegistration;

	@ColumnWidth(20)
	@ExcelProperty("家庭住址")
	private String homeAddress;

	@ExcelIgnore
	@ExcelProperty("政治面貌key")
	private Integer politicalStatus;

	@ColumnWidth(12)
	@ExcelProperty("政治面貌")
	private String politicalStatusName;

	@ExcelIgnore
	@ExcelProperty("文化程度key")
	private Integer eduLevel;

	@ColumnWidth(12)
	@ExcelProperty("文化程度")
	private String eduLevelName;

	@ColumnWidth(15)
	@ExcelProperty("毕业院校")
	private String eduSchool;

	@ColumnWidth(15)
	@ExcelProperty("毕业专业")
	private String eduMajor;

	@ExcelIgnore
	@ExcelProperty("考勤机用户Id")
	private Long zkecoUserId;

	@ColumnWidth(20)
	@ExcelProperty("考勤机用户编号")
	private String zkecoBadgeNumber;
	@ColumnWidth(20)
	@ExcelProperty("状态")
	private String status;
}
