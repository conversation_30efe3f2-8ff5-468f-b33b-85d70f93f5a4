/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.excel;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;
import org.springblade.modules.system.service.IUserHealthRecordsService;
import org.springblade.modules.system.service.IUserService;

/**
 * 用户数据导入类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class UserHealthRecordImporter implements ExcelImporter<UserHealthRecordExcel> {

	private final IUserHealthRecordsService service;
	private final String type;

	@Override
	public void save(List<UserHealthRecordExcel> data) {
		service.importData(data,type);
	}
}
