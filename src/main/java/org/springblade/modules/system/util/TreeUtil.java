package org.springblade.modules.system.util;

import org.apache.commons.lang3.StringUtils;
import org.springblade.modules.system.entity.Post;
import org.springblade.modules.system.vo.DeptAndUserVO;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024-07011
 */
public class TreeUtil {


	/**
	 * 部门集合和用户集合 封装成树形结构
	 *
	 * @param deptList 一维集合
	 * @param userList 用户集合
	 * @return 树形结构数据
	 */
	public static List<DeptAndUserVO> buildTree(List<DeptAndUserVO> deptList, List<DeptAndUserVO> userList) {
		Map<Long, DeptAndUserVO> map = new HashMap<>();
		List<DeptAndUserVO> result = new ArrayList<>();
		for (DeptAndUserVO node : deptList) {
			map.put(node.getId(), node);
		}
		//转为树形结构
		for (DeptAndUserVO node : deptList) {
			DeptAndUserVO parent = map.get(node.getParentId());
			if (parent != null && !Objects.equals(node.getDeptCategory(), DeptAndUserVO.DEPT_CATEGORY)) {
				parent.addChild(node);
			} else {
				result.add(node);
			}
		}

		// 将用户添加到对应的部门中
		for (DeptAndUserVO user : userList) {
			Long deptId = null;
			//暂时支持一个人只有一个部门的，一个人有多个部门的，没有处理。现在系统中也不支持一个人多个部门
			String deptIdStr = user.getDeptId();

			if (StringUtils.isNotBlank(deptIdStr)) {
				if (deptIdStr.matches("-?\\d+")) {
					deptId = Long.parseLong(deptIdStr);
				}
			}
			if (deptId != null) {
				DeptAndUserVO dept = map.get(deptId);
				if (dept != null) {

					//最小部门，改成上一级部门
					if (Objects.equals(DeptAndUserVO.DEPT_CATEGORY, dept.getDeptCategory())) {
						dept = map.get(dept.getParentId());
					}

					dept.addChild(user);
				}
			}

		}


		Optional<DeptAndUserVO> company = deptList.stream().filter(item -> item.getDeptCategory() == 1).findFirst();

		company.ifPresent(TreeUtil::updateEmployeeCount);

		return result;
	}


	/**
	 * 计算它所有子部门的人数，叠加到一起
	 *
	 * @param dept 部门数据
	 */
	private static Integer updateEmployeeCount(DeptAndUserVO dept) {
		// 如果是用户节点，则不需要进一步处理
		if (DeptAndUserVO.USER_TYPE.equals(dept.getType())) {
			return 0;
		}

		// 当前部门人数
		int totalEmployees = dept.getEmployeeCount();

		// 遍历所有子节点
		for (DeptAndUserVO child : dept.getChildren()) {
			// 将子节点的员工数加到总数中
			totalEmployees += updateEmployeeCount(child);
		}

		// 更新当前部门的员工数（包括其子部门的员工数）
		dept.setEmployeeCount(totalEmployees);

		return totalEmployees;
	}


	public static void bindingPost(List<DeptAndUserVO> userList, List<Post> postList) {

		userList.forEach(user -> postList.forEach(post -> {

			String postIdListStr = user.getPostId();

			if (StringUtils.isNotBlank(postIdListStr)) {
				String[] postIdArray = postIdListStr.split(",");

				for (String postIdStr : postIdArray) {

					Long postId = Long.parseLong(postIdStr);

					if (Objects.equals(post.getId(), postId)) {
						user.getTag().add(post.getPostName());
					}
				}

			}
		}));

	}

}
