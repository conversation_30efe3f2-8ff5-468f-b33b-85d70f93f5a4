/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 小程序菜单 实体类
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@TableName("blade_app_menu")
@ApiModel(value = "AppMenu对象", description = "小程序菜单")
@EqualsAndHashCode(callSuper = true)
public class AppMenu extends TenantEntity {

  private static final long serialVersionUID = 2161692860884184363L;
  /**
   * 备注
   */
  @ApiModelProperty(value = "备注")
  private String remark;
  /**
   * 父级菜单
   */
  @ApiModelProperty(value = "父级菜单")
  private Long parentId;
  /**
   * 菜单编号
   */
  @ApiModelProperty(value = "菜单编号")
  private String code;
  /**
   * 菜单名称
   */
  @ApiModelProperty(value = "菜单名称")
  private String name;
  /**
   * 排序
   */
  @ApiModelProperty(value = "排序")
  private Integer sort;
  /**
   * 菜单类型
   */
  @ApiModelProperty(value = "菜单类型")
  private Integer category;
  /**
   * 图片
   */
  @ApiModelProperty(value = "图片")
  private String icon;
  /**
   * 图片
   */
  @ApiModelProperty(value = "路由地址")
  private String path;



}
