
package org.springblade.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 健康记录 实体类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@TableName("ni_user_health_records")
@ApiModel(value = "UserHealthRecords对象", description = "健康记录")
@EqualsAndHashCode(callSuper = true)
public class UserHealthRecordsEntity extends TenantEntity {

  private static final long serialVersionUID = -1086593628166324856L;
  /**
   * 入职体检
   */
  public static final String TYPE_ONBOARDING = "1";
  /**
   * 员工体检
   */
  public static final String TYPE_EMPLOYEE = "2";
  /**
   * 体重登记
   */
  public static final String TYPE_WEIGHT = "3";
  /**
   * 体重状态-1:偏瘦
   */
  public static final String WEIGHT_STATUS_THINNESS = "1";
  /**
   * 体重状态 2:正常
   */
  public static final String WEIGHT_STATUS_NORMAL = "2";
  /**
   * 体重状态- 3:偏胖
   */
  public static final String WEIGHT_STATUS_OVERWEIGHT = "3";
  /**
   * 体重状态-4:肥胖
   */
  public static final String WEIGHT_STATUS_OBESITY = "4";
  /**
   * 血压状态-1:低血压
   */
  public static final String BLOOD_PRESSURE_STATUS_LOW = "1";
  /**
   * 血压状态-2:正常
   */
  public static final String BLOOD_PRESSURE_STATUS_NORMAL = "2";
  /**
   * 血压状态-3:高血压
   */
  public static final String BLOOD_PRESSURE_STATUS_HIGH = "3";
  /**
   * 低血糖
   */
  public static final String BLOOD_SUGAR_STATUS_LOW = "1";
  /**
   * 血糖正常
   */
  public static final String BLOOD_SUGAR_STATUS_NORMAL = "2";
  /**
   * 高血糖
   */
  public static final String BLOOD_SUGAR_STATUS_HIGH = "3";
  /**
   * 血脂-正常
   */
  public static final String BLOOD_LIPIDS_STATUS_NORMAL = "1";
  /**
   * 高血脂
   */
  public static final String BLOOD_LIPIDS_STATUS_HIGH = "2";
  /**
   * 更多
   */
  @ApiModelProperty(value = "更多")
  private String more;
  /**
   * 体重
   */
  @ApiModelProperty(value = "身高")
  private Float height;
  /**
   * 血压状态
   */
  @ApiModelProperty(value = "血压状态")
  private String bloodPressureStatus;
  /**
   * 体重状态
   */
  @ApiModelProperty(value = "体重状态")
  private String weightStatus;
  /**
   * 血压低压
   */
  @ApiModelProperty(value = "血压低压")
  private Integer bloodPressureMin;

  /**
   * 血压高压
   */
  @ApiModelProperty(value = "血压高压")
  private Integer bloodPressureMax;

  /**
   * 血糖
   */
  @ApiModelProperty(value = "血糖")
  private Float bloodSugar;
  /**
   * 血糖状态
   */
  @ApiModelProperty(value = "血糖状态")
  private String bloodSugarStatus;
  /**
   * 体重
   */
  @ApiModelProperty(value = "体重")
  private Float weight;
  /**
   * bmi
   */
  @ApiModelProperty(value = "bmi")
  private Float bmi;
  /**
   * 用户id
   */
  @ApiModelProperty(value = "用户id")
  private Long userId;
  /**
   * 尿糖
   */
  @ApiModelProperty(value = "尿糖")
  private Float glucose;

  /**
   * 体检日期
   */
  @ApiModelProperty(value = "体检日期")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date examinationDate;

  /**
   * 甘油三酯
   */
  @ApiModelProperty(value = "甘油三酯")
  private Float triglycerides;

  /**
   * 胆固醇
   */
  @ApiModelProperty(value = "胆固醇")
  private Float cholesterol;
  /**
   * 高密度脂蛋白胆固醇
   */
  @ApiModelProperty(value = "高密度脂蛋白胆固醇")
  private Float hdl;

  /**
   * 血脂状态
   */
  @ApiModelProperty(value = "血脂状态")
  private String bloodLipidsStatus;
  /**
   * 类型
   */
  private String type;
}
