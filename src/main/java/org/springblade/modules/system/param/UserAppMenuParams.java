package org.springblade.modules.system.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Data
public class UserAppMenuParams {

	@NotNull
	@Size(min = 1,max = 8,message = "请至少上传1一条记录,至多8条记录")
	@ApiModelProperty(value = "菜单集合", required = true)
	private List<@Valid AppMenuParam> userAppMenuParams;

	@Data
	public static class AppMenuParam {
		/**
		 *
		 */
		@NotNull(message = "菜单id不能为空")
		@ApiModelProperty(value = "菜单主键id", required = true)
		private Long id;

		/**
		 * 排序
		 */
		@NotNull
		@ApiModelProperty(value = "排序", required = true)
		private Integer sort;

	}

}
