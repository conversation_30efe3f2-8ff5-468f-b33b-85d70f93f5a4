/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.system.entity.User;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserVO对象", description = "UserVO对象")
public class UserVO extends User {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 密码
	 */
	@JsonIgnore
	private String password;

	/**
	 * 租户名
	 */
	private String tenantName;

	/**
	 * 用户平台名
	 */
	private String userTypeName;

	/**
	 * 角色名
	 */
	private String roleName;

	/**
	 * 部门名
	 */
	private String deptName;

	/**
	 * 顶级部门名
	 */
	private String topDeptName;

	private String topDeptId;

	/**
	 * 岗位名
	 */
	private String postName;

	/**
	 * 性别
	 */
	private String sexName;

	/**
	 * 拓展信息
	 */
	private String userExt;

	/**
	 * 附件
	 */
	private List<Map<String, Object>> attachment;

	/**
	 * 劳动合同
	 */
	private List<ContractItem> contracts;
	private Integer contractTotal;

	private String contractSN;
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate contractStart;
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate contractEnd;
	private String contractName;
	private Integer signedContract;

	/**
	 * 生日 筛选左区间
	 */
	@ApiModelProperty(value = "生日 筛选左区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate birthdayStart;
	/**
	 * 生日 筛选右区间
	 */
	@ApiModelProperty(value = "生日 筛选右区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate birthdayEnd;
	/**
	 * 入职时间 筛选左区间
	 */
	@ApiModelProperty(value = "入职时间 筛选左区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate entryTimeStart;
	/**
	 * 入职时间 筛选右区间
	 */
	@ApiModelProperty(value = "入职时间 筛选右区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate entryTimeEnd;

		/**
	 * 入职时间 筛选左区间
	 */
	@ApiModelProperty(value = "入职时间 筛选左区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate leaveTimeStart;
	/**
	 * 入职时间 筛选右区间
	 */
	@ApiModelProperty(value = "入职时间 筛选右区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate leaveTimeEnd;

	private String avatarUrl;

	/**
	 * 人员分布（省内/省外）
	 */
	private Boolean userProvince;


}
