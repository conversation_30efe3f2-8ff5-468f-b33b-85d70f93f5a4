/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.system.entity.UserHealthRecordsEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 健康记录 视图实体类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
public class UserHealthWeightStatsVO implements Serializable {


  private static final long serialVersionUID = 6234642148207448241L;
  /**
   * 总数
   */
  private Long total;
  /**
   * 已登记数
   */
  private Long enrolled;
  /**
   * 未登记数
   */
  private Long unEnrolled;
  /**
   * 肥胖
   */
  private Long obesity;
  /**
   * 超重
   */
  private Long overweight;
  /**
   * 正常
   */
  private Long normal;
}
