package org.springblade.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;

@Data
@ApiModel(value = "定时任务对象", description = "定时任务对象")
public class JobVO implements Serializable {

	private static final long serialVersionUID = -9135240729824065825L;
	@NotBlank(message = "任务类不能为空")
	@ApiModelProperty(value = "任务类路径", required = true)
	private String jobClazz;

	@NotBlank(message = "任务类名不能为空")
	@ApiModelProperty(value = "任务类名", required = true)
	private String jobName;
	/**
	 * 组名+任务类key组成唯一标识，所以如果这个参数为空，那么默认以任务类key作为组名
	 */
	@ApiModelProperty(value = "任务组名，命名空间")
	private String jobGroup;

	@ApiModelProperty(value = "任务数据")
	private List<DefaultKeyValue<String, String>> jobData;

	@ApiModelProperty(value = "cron表达式")
	private String cron;

	@ApiModelProperty(value = "描述")
	private String description;

	private String triggerState;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private List<Date> recentFireTimeList;
}
