package org.springblade.modules.system.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springblade.core.tool.node.BaseNode;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.core.tool.utils.Func;

import java.util.Objects;

public class TreeNodeVO extends BaseNode<TreeNode> {
	private static final long serialVersionUID = 1L;
	private String label;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long key;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long value;

	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		} else if (obj == null) {
			return false;
		} else {
			TreeNode other = (TreeNode)obj;
			return Func.equals(this.getId(), other.getId());
		}
	}

	public int hashCode() {
		return Objects.hash(new Object[]{this.id, this.parentId});
	}

	public TreeNodeVO() {
	}

	public String getLabel() {
		return this.label;
	}

	public Long getKey() {
		return this.key;
	}

	public Long getValue() {
		return this.value;
	}

	public void setLabel(final String label) {
		this.label = label;
	}

	public void setKey(final Long key) {
		this.key = key;
	}

	public void setValue(final Long value) {
		this.value = value;
	}

	public String toString() {
		return "TreeNode(label=" + this.getLabel() + ", key=" + this.getKey() + ", value=" + this.getValue() + ")";
	}
}
