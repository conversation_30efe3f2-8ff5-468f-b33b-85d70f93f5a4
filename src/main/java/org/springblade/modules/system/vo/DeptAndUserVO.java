/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.vo;

import lombok.Data;
import org.springblade.modules.system.entity.Dept;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
public class DeptAndUserVO {
	private static final long serialVersionUID = 1L;

	public final static String USER_TYPE = "user";

	public final static String DEPT_TYPE = "department";

	public final static Integer DEPT_CATEGORY = 4;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 上一级节点
	 */
	private Long parentId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 部门id（type为user时才会用到此字段）
	 */
	private String deptId;

	/**
	 * 类型 department代表部门 user代表用户
	 */
	private String type;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 岗位tag
	 */
	private List<String> tag = new ArrayList<>();

	/**
	 * 岗位Id
	 */
	private String postId;

	/**
	 * 员工数量
	 */
	private Integer employeeCount = 0;

	/**
	 * 部门级别
	 */
	private Integer deptCategory;

	/**
	 * 子节点
	 */
	private List<DeptAndUserVO> children = new ArrayList<>();


	// 添加子部门或用户
	public void addChild(DeptAndUserVO child) {
		this.children.add(child);
		if (Objects.equals(USER_TYPE, child.getType())) {
			employeeCount++; // 如果是用户，则部门人数加1
		}
	}

}
