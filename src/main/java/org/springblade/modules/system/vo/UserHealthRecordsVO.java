/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.system.entity.UserHealthRecordsEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 健康记录 视图实体类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserHealthRecordsVO extends UserHealthRecordsEntity {


  private static final long serialVersionUID = -3156612583663368005L;
  private String userName;

  private String deptId;

  private String deptName;
  /**
   * 用户状态
   */
  private Integer userStatus;
  /**
   * 体检年份
   */
  private Integer year;
  /**
   * 标签
   */
  private List<Abnormal> tags;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate startDate;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDate endDate;

  public enum Abnormal {
    /**
     * 超重
     */
    OVERWEIGHT,
    /**
     * 肥胖
     */
    OBESITY,
    /**
     * 偏瘦
     */
    THINNESS,
    /**
     * 高血压
     */
    HYPERTENSION,
    /**
     * 低血压
     */
    LOW_BLOOD_PRESSURE,
    /**
     * 高血脂
     */
    HYPERLIPIDEMIA,
    /**
     * 高血糖
     */
    HYPERGLYCEMIA,
    /**
     * 脂肪肝
     */
    FATTY_LIVER;

  }

}
