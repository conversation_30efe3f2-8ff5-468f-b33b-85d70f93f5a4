package org.springblade.modules.system.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class ContractItem implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 开始日期
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate contractStart;

  /**
   * 结束日期
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate contractEnd;

  /**
   * 附件id
   */
  private Long id;

  /**
   * 附件id
   */
  private Long linkId;

  /**
   * 是否生效
   */
  private Boolean effect;

  /**
   * 附件名
   */
  @TableField(exist = false)
  private String name;

  /**
   * 附件原文件名
   */
  @TableField(exist = false)
  private String originalName;

  /**
   * 附件大小
   */
  @TableField(exist = false)
  private Long attachSize;

  /**
   * 创建人
   */
  @TableField(exist = false)
  private String createUserName;
  /**
   * 类似于id
   */
  private String createSN;

  /**
   * 创建时间
   */
  @TableField(exist = false)
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  public void setIdByLink() {
    this.id = this.linkId;
  }

}
