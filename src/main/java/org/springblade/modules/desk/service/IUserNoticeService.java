/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.desk.dto.NoticeDTO;
import org.springblade.modules.desk.entity.Notice;
import org.springblade.modules.desk.entity.UserNoticeEntity;
import org.springblade.modules.desk.vo.NoticeVO;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用户消息表 服务类
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
public interface IUserNoticeService extends IService<UserNoticeEntity> {

	/**
	 * 自定义分页
	 */
	IPage<NoticeVO> selectUserNoticeVOPage(IPage<NoticeVO> page, NoticeDTO notice, Long userId);

	/**
	 * 暂存 或 发布
	 *
	 * @param notice
	 * @return
	 */
	Boolean saveOrPublish(NoticeVO notice);

	/**
	 * Notice -> NoticeVO
	 *
	 * @param notice
	 * @param userNotice
	 * @return
	 */
	NoticeVO entityNoticeVO(Notice notice, UserNoticeEntity userNotice);

	/**
	 * Notice -> NoticeVO
	 *
	 * @param notice
	 * @param userId -> userNotice
	 * @return entityNoticeVO(notice, userNotice)
	 */
	NoticeVO entityNoticeVO(Notice notice, Long userId);

	/**
	 * 未读用户
	 *
	 * @param notice
	 * @return
	 */
	String gatherUserMessage(Notice notice);

	/**
	 * 已读
	 *
	 * @param ids
	 * @return
	 */
	Boolean change2Read(@NotEmpty List<Long> ids);

	/**
	 * 已读
	 *
	 * @param ids
	 * @return
	 */
	Boolean change2Read(@NotEmpty List<Long> ids,String comment);

	/**
	 * 查询未读消息数量
	 *
	 * @param userId 用户id
	 * @return 未读消息数量
	 */
	long unReadCount(Long userId);

	/**
	 * 发送消息通知给用户
	 *
	 * @param type         消息通知类型  notice是通用类型消息通知，contractNotice是合同通知
	 * @param title        通知的标题
	 * @param content      通知的内容
	 * @param receiveUsers 需要通知给哪些用户
	 */

	void pushNotice(String type, String title, String content, List<Long> receiveUsers);

	/**
	 * 发送消息通知给用户
	 *
	 * @param type         消息通知类型  notice是通用类型消息通知，contractNotice是合同通知 birthday生日通知跳转
	 * @param router       跳转路由
	 * @param title        通知的标题
	 * @param content      通知的内容
	 * @param receiveUsers 需要通知给哪些用户
	 */

	void pushNotice(String type, String router, String title, String content, List<Long> receiveUsers);


	/**
	 * 仅发送socket给用户,不保存notice
	 *
	 * @param router       跳转的路由
	 * @param title        通知的标题
	 * @param content      通知的内容
	 * @param receiveUsers 需要通知给哪些用户
	 */
	void sendSocket(String router, String title, String type, String content, List<Long> receiveUsers);
}
