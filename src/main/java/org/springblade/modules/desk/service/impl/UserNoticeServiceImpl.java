/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.service.impl;

import static org.springblade.modules.desk.entity.UserNoticeEntity.NOTICE_STATUS_PUBLISH;
import static org.springblade.modules.desk.entity.UserNoticeEntity.NOTICE_STATUS_READ;
import static org.springblade.modules.desk.entity.UserNoticeEntity.NOTICE_STATUS_WITHDRAW;
import static org.springblade.modules.desk.entity.UserNoticeEntity.Need_Manager_Response;
import static org.springblade.modules.desk.entity.UserNoticeEntity.RELEASE_TARGET_ALL;
import static org.springblade.modules.desk.entity.UserNoticeEntity.RELEASE_TARGET_PART;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.jsonwebtoken.lang.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.constraints.NotEmpty;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.UserCache;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.desk.dto.NoticeDTO;
import org.springblade.modules.desk.entity.Notice;
import org.springblade.modules.desk.entity.UserNoticeEntity;
import org.springblade.modules.desk.mapper.NoticeMapper;
import org.springblade.modules.desk.mapper.UserNoticeMapper;
import org.springblade.modules.desk.service.INoticeService;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.desk.vo.NoticeVO;
import org.springblade.modules.notification.dto.NotificationDto;
import org.springblade.modules.notification.event.NotificationEvent;
import org.springblade.modules.notification.event.NotificationEvent.Type;
import org.springblade.modules.resource.entity.Attach;
import org.springblade.modules.resource.service.IAttachService;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户消息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@Service
@RequiredArgsConstructor
public class UserNoticeServiceImpl extends
	ServiceImpl<UserNoticeMapper, UserNoticeEntity> implements IUserNoticeService {

	private final IUserService userService;
	private final INoticeService iNoticeService;
	private final IAttachService attachService;
	private final IDeptService deptService;
	private final NoticeMapper noticeMapper;

	@Override
	public IPage<NoticeVO> selectUserNoticeVOPage(IPage<NoticeVO> page, NoticeDTO notice,
		Long userId) {
		return page.setRecords(baseMapper.selectUserNoticeVOPage(page, notice, userId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveOrPublish(NoticeVO notice) {
		Integer status = notice.getStatus();
		Assert.isTrue(iNoticeService.saveOrUpdate(notice), "公告保存失败");
		Long noticeId = notice.getId();
		if (status == NOTICE_STATUS_PUBLISH) {
			Integer releaseTarget = notice.getReleaseTarget();
			List<UserNoticeEntity> userNotices = new ArrayList<>();
			List<Long> userIds = new ArrayList<>();
			List<Long> leads = new ArrayList<>();
			Integer isManagerResponse = notice.getIsManagerResponse();
			if (isManagerResponse == Need_Manager_Response) {
				leads = deptService
					.list(Wrappers.<Dept>lambdaQuery().isNotNull(Dept::getLeaderId)).stream()
					.map(Dept::getLeaderId).collect(Collectors.toList());
			}
			final List<Long> finalLeads = leads;
			if (releaseTarget == RELEASE_TARGET_ALL) {
				List<Long> collect = userService.list().stream().map(user -> user.getId())
					.collect(Collectors.toList());
				collect.stream().forEach(id -> {
					UserNoticeEntity userNotice = new UserNoticeEntity();
					userNotice.setNoticeId(noticeId);
					userNotice.setReceiveUser(id);
					userNotice.setIsRead(0);
					if (finalLeads.contains(id)) {
						userNotice.setComment("待回复意见");
					}
					userNotices.add(userNotice);
					userIds.add(id);
				});
				if (null != notice.getReceiveUsers()) {
					LambdaUpdateWrapper<Notice> updateWrapper = new LambdaUpdateWrapper<Notice>()
						.set(Notice::getReceiveUsers, null)
						.eq(Notice::getId, notice.getId());
					iNoticeService.update(updateWrapper);
				}
			}
			if (releaseTarget == RELEASE_TARGET_PART) {
				String strUserIds = notice.getReceiveUsers();
				String[] arrUserIds = strUserIds.split(",");
				Arrays.stream(arrUserIds).forEach(id -> {
					if (StringUtil.isNumeric(id)) {
						UserNoticeEntity userNotice = new UserNoticeEntity();
						userNotice.setNoticeId(noticeId);
						Long receiver = Long.parseLong(id);
						userNotice.setReceiveUser(receiver);
						userNotice.setIsRead(0);
						if (finalLeads.contains(receiver)) {
							userNotice.setComment("待回复意见");
						}
						userNotices.add(userNotice);
						userIds.add(Long.parseLong(id));
					}
				});
			}
			Assert.isTrue(this.saveOrUpdateBatch(userNotices), "公告发布失败");
			NotificationDto notificationDto = new NotificationDto();
			notificationDto.setCode("notice");
			notificationDto.setTitle(notice.getTitle());
			notificationDto.setType("info");
			notificationDto.setBusinessId(String.valueOf(notice.getId()));
			notificationDto.setContent(String
				.format("%s 在 %s 发布了通知公告!",
					UserCache.getUser(notice.getCreateUser()).getName(),
					DateUtil.format(notice.getReleaseTime(), "yyyy-MM-dd HH:mm:ss")));
			notificationDto.setReceiveType(NotificationDto.RECEIVE_TYPE_USER);
			notificationDto.setReceiverId(userIds);
			SpringUtil.publishEvent(new NotificationEvent(notificationDto));
		}
		if (status == NOTICE_STATUS_WITHDRAW) {
			LambdaQueryWrapper<UserNoticeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<UserNoticeEntity>()
				.eq(UserNoticeEntity::getNoticeId, noticeId);
			Assert.isTrue(this.baseMapper.delete(lambdaQueryWrapper) > 0, "公告撤销失败");
		}
		//附件
		List<Map<String, Object>> attachment = notice.getAttachment();
		if (attachment != null) {
			List<Attach> attaches = attachService.listByIds(
				attachment.stream().map(map -> String.valueOf(map.get("value")))
					.collect(Collectors.toSet()));
			attaches.forEach(attach -> {
				attach.setBusinessName("blade_desk_notice");
				attach.setBusinessKey(notice.getId() + "");
			});
			attachService.saveOrUpdateBatch(attaches);
		}
		return true;
	}

	@Override
	public NoticeVO entityNoticeVO(Notice notice, UserNoticeEntity userNotice) {
		NoticeVO noticeVO = Objects.requireNonNull(BeanUtil.copy(notice, NoticeVO.class));
		if (Func.isNotEmpty(userNotice)) {
			noticeVO.setUserNoticeId(userNotice.getId());
			noticeVO.setIsRead(userNotice.getIsRead());
			noticeVO.setComment(userNotice.getComment());
			if (userNotice.getReceiveUser() != null) {
				User user = UserCache.getUser(userNotice.getReceiveUser());
				noticeVO.setReceiveUserName(user.getRealName());
				noticeVO.setReceiveTime(userNotice.getReceiveTime());
			}
		}
		return noticeVO;
	}

	@Override
	public NoticeVO entityNoticeVO(Notice notice, Long userId) {
		UserNoticeEntity userNotice = this.getOne(
			new LambdaQueryWrapper<UserNoticeEntity>().eq(UserNoticeEntity::getReceiveUser, userId)
				.eq(UserNoticeEntity::getNoticeId, notice.getId()));
		return entityNoticeVO(notice, userNotice);
	}

	@Override
	public String gatherUserMessage(Notice notice) {
		LambdaQueryWrapper<UserNoticeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<UserNoticeEntity>()
			.eq(UserNoticeEntity::getNoticeId, notice.getId());
		List<UserNoticeEntity> list = this.list(lambdaQueryWrapper);
		List<Long> readUsers = list.stream().filter(i -> i.getIsRead() == NOTICE_STATUS_READ)
			.map(UserNoticeEntity::getReceiveUser).collect(Collectors.toList());
		List<Long> unreadUsers = list.stream().filter(i -> i.getIsRead() != NOTICE_STATUS_READ)
			.map(UserNoticeEntity::getReceiveUser).collect(Collectors.toList());

		String readNames = readUsers.stream().map(UserCache::getUser)
			.filter(i -> null != i).map(User::getName).collect(Collectors.joining(","));
		String unreadNames = unreadUsers.stream().map(UserCache::getUser)
			.filter(i -> null != i).map(User::getName).collect(Collectors.joining(","));

		List<UserNoticeEntity> userAndComment = list.stream()
			.filter(i -> null != i.getComment()).collect(Collectors.toList());
		StringJoiner joiner = new StringJoiner("{^com$}", "{^sp$}", "");
		userAndComment.stream().forEach(o -> {
			User user = UserCache.getUser(o.getReceiveUser());
			joiner.add(user.getName() + "{^name$}" + o.getComment());
		});
		return readUsers.size() + ":" + readNames + "{^sp$}" + unreadUsers.size() + ":"
			+ unreadNames + joiner.toString();
	}

	@Override
	public Boolean change2Read(@NotEmpty List<Long> ids) {
		List<UserNoticeEntity> userNoticeEntities = new ArrayList<>();
		ids.forEach(id -> {
			UserNoticeEntity userNoticeEntity = new UserNoticeEntity();
			userNoticeEntity.setId(id);
			userNoticeEntity.setIsRead(NOTICE_STATUS_READ);
			userNoticeEntity.setReceiveTime(LocalDateTime.now());
			userNoticeEntities.add(userNoticeEntity);
		});
		return this.updateBatchById(userNoticeEntities);
	}

	@Override
	public Boolean change2Read(@NotEmpty List<Long> ids, String comment) {
		List<UserNoticeEntity> userNoticeEntities = new ArrayList<>();
		ids.forEach(id -> {
			UserNoticeEntity userNoticeEntity = new UserNoticeEntity();
			userNoticeEntity.setId(id);
			userNoticeEntity.setIsRead(NOTICE_STATUS_READ);
			userNoticeEntity.setComment(comment);
			userNoticeEntity.setReceiveTime(LocalDateTime.now());
			userNoticeEntities.add(userNoticeEntity);
		});
		return this.updateBatchById(userNoticeEntities);
	}

	@Override
	public long unReadCount(Long userId) {
		return this.baseMapper.getUnReadCount(userId);
	}

	@Async
	@Override
	public void pushNotice(String type, String title, String content, List<Long> receiveUsers) {

		this.pushNoticeFull(type, StringUtils.EMPTY, title, content, receiveUsers);
	}

	@Async
	@Override
	public void pushNotice(String type, String router, String title, String content,
		List<Long> receiveUsers) {
		this.pushNoticeFull(type, router, title, content, receiveUsers);
	}

	private void pushNoticeFull(String type, String router, String title, String content,
		List<Long> receiveUsers) {
		Objects.requireNonNull(receiveUsers, "接收用户不能为空");

		String receiveUsersStr = receiveUsers.stream().map(String::valueOf)
			.collect(Collectors.joining(","));

		// Notice和UserNoticeEntity是一对多的关系
		//保存一个Notice
		Notice notice = new Notice();
		notice.setTitle(title);
		notice.setReleaseTarget(1);
		notice.setReceiveUsers(receiveUsersStr);
		notice.setIsManagerResponse(0);
		notice.setStatus(1);
		notice.setCategory(1);
		notice.setContent(content);
		notice.setReleaseTime(new Date());
		notice.setCreateUser(1639150025229062145L);
		notice.setTenantId("000000");
		notice.setJumpUrl(router);
		notice.setIsDeleted(0);
		noticeMapper.insert(notice);
		//根据要推送的用户数量来保存UserNoticeEntity的数量
		receiveUsers.parallelStream().forEach(userId -> {
			UserNoticeEntity userNoticeEntity = new UserNoticeEntity();
			userNoticeEntity.setReceiveUser(userId);
			userNoticeEntity.setNoticeId(notice.getId());
			userNoticeEntity.setIsRead(0);
			this.save(userNoticeEntity);
		});
		NotificationDto notificationDto = new NotificationDto();
		notificationDto.setCode("notice");
		notificationDto.setTitle(notice.getTitle());
		notificationDto.setType(type);
		notificationDto.setBusinessId(String.valueOf(notice.getId()));
		notificationDto.setContent(content);
		notificationDto.setReceiveType(NotificationDto.RECEIVE_TYPE_USER);
		notificationDto.setReceiverId(receiveUsers);
		SpringUtil.publishEvent(new NotificationEvent(notificationDto));
	}

	@Override
	public void sendSocket(String router, String title, String type, String content,
		List<Long> receiveUsers) {
		Objects.requireNonNull(receiveUsers, "接收用户不能为空");
		NotificationDto notificationDto = new NotificationDto();
		notificationDto.setCode(router);
		notificationDto.setTitle(title);
		notificationDto.setType(type);
		notificationDto.setContent(content);
		notificationDto.setReceiveType(NotificationDto.RECEIVE_TYPE_USER);
		notificationDto.setReceiverId(receiveUsers);
		SpringUtil.publishEvent(new NotificationEvent(notificationDto, Type.SOCKET));

	}

}
