package org.springblade.modules.desk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.desk.entity.Notice;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 通知公告视图类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeVO extends Notice {

	private static final long serialVersionUID = -4329631432501555962L;
	@ApiModelProperty(value = "通知类型名")
	private String categoryName;

	@ApiModelProperty(value = "租户编号")
	private String tenantId;

	/**
	 * 用户消息id
	 */
	@ApiModelProperty(value = "用户消息id")
	private Long userNoticeId;

	/**
	 * 是否已读 0否 1是
	 */
	@ApiModelProperty(value = "是否已读 0否 1是")
	private Integer isRead;

	/**
	 * 回复意见
	 */
	@ApiModelProperty(value = "回复意见")
	private String comment;

	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "接收人")
	private String receiveUserName;
	/**
	 * 接收时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime receiveTime;

	/**
	 * 附件
	 */
	private List<Map<String, Object>> attachment;

}
