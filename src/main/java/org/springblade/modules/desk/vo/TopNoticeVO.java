package org.springblade.modules.desk.vo;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.modules.notification.dto.NotificationDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/2/9 9:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TopNoticeVO implements Serializable {

  private static final long serialVersionUID = 7600097137993889436L;
  List<Notify> notifies;
  List<Popper> poppers;

  @Data
  @NoArgsConstructor
  public static class Popper implements Serializable {

    private String code;
    private String type;
    private String title;
    private String msg;

    public Popper(NotificationDto notificationDto) {
      super();
      this.code = notificationDto.getCode();
      this.title = notificationDto.getTitle();
      this.msg = notificationDto.getContent();
      this.type = notificationDto.getType();
    }

  }

  @NoArgsConstructor
  @AllArgsConstructor
  @Data
  public static class Notify implements Serializable {

    private String code;
    private long num;
  }
}
