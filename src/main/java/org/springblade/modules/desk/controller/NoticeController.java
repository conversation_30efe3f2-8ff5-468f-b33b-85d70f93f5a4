/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.UserCache;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.TenantDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.desk.entity.Notice;
import org.springblade.modules.desk.service.INoticeService;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.desk.vo.NoticeVO;
import org.springblade.modules.desk.wrapper.NoticeWrapper;
import org.springblade.modules.system.entity.User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@TenantDS
@RestController
@RequestMapping(AppConstant.APPLICATION_DESK_NAME + "/notice")
@AllArgsConstructor
@ApiSort(2)
@Api(value = "用户博客", tags = "博客接口")
public class
NoticeController extends BladeController {

	private final INoticeService noticeService;
	private final IUserNoticeService userNoticeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入notice")
	public R<NoticeVO> detail(Notice notice) {
		Notice detail = noticeService.getOne(Condition.getQueryWrapper(notice));
		return R.data(NoticeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "category", value = "公告类型", paramType = "query", dataType = "integer"),
		@ApiImplicitParam(name = "title", value = "公告标题", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<IPage<NoticeVO>> list(@ApiIgnore @RequestParam Map<String, Object> notice,
		Query query) {
		NoticeWrapper.build().noticeQuery(notice);
		IPage<Notice> pages = noticeService.page(Condition.getPage(query),
			Condition.getQueryWrapper(notice, Notice.class).orderByDesc("create_time"));
		return R.data(NoticeWrapper.build().pageVO(pages));
	}

	/**
	 * 多表联合查询自定义分页
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "category", value = "公告类型", paramType = "query", dataType = "integer"),
		@ApiImplicitParam(name = "title", value = "公告标题", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入notice")
	public R<IPage<NoticeVO>> page(@ApiIgnore NoticeVO notice, Query query) {
		IPage<NoticeVO> pages = noticeService.selectNoticePage(Condition.getPage(query), notice);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入notice")
	public R save(@RequestBody Notice notice) {
		return R.status(noticeService.save(notice));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入notice")
	public R update(@RequestBody Notice notice) {
		return R.status(noticeService.updateById(notice));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入notice")
	public R submit(@RequestBody NoticeVO notice) {
		notice.setCreateUser(AuthUtil.getUserId());
		return R.status(userNoticeService.saveOrPublish(notice));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入notice")
	public R remove(@ApiParam(value = "主键集合") @RequestParam String ids) {
		boolean temp = noticeService.deleteLogic(Func.toLongList(ids));
		return R.status(temp);
	}

	/**
	 * 汇总用户信息
	 */
	@GetMapping("/userMessage")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "汇总用户信息", notes = "传入noticeId")
	public R<String> gatherUserMessage(Notice notice) {
		return R.data(userNoticeService.gatherUserMessage(notice));
	}

	/**
	 * 用户界面-详情
	 */
	@GetMapping("/user-detail")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "详情", notes = "传入notice")
	public R<NoticeVO> userDetail(Notice notice) {
		Notice detail = noticeService.getOne(Condition.getQueryWrapper(notice));
		Long userId = AuthUtil.getUserId();
		NoticeVO noticeVO = userNoticeService.entityNoticeVO(detail, userId);
		Long createUser = noticeVO.getCreateUser();
		if (Objects.nonNull(createUser)) {
			User user = UserCache.getUser(createUser);
			noticeVO.setCreateUserName(user.getName());
		}
		//如果是需要手动回复的，则不再修改状态
		if (detail.getIsManagerResponse() == null || detail.getIsManagerResponse() == 0) {
			userNoticeService.change2Read(Collections.singletonList(noticeVO.getUserNoticeId()));
		}
		return R.data(noticeVO);
	}


}
