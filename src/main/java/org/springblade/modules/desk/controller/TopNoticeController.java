package org.springblade.modules.desk.controller;

import com.google.common.collect.ImmutableList;
import io.swagger.annotations.Api;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.desk.vo.TopNoticeVO.Notify;
import org.springblade.plugin.workflow.process.service.impl.WfProcessService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.1
 * @since 2023/9/19
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_DESK_NAME + "/top-notice")
@Api(value = "通知提醒", tags = "通知提醒")
public class TopNoticeController extends BladeController {

	private final WfProcessService wfProcessService;

	private final IUserNoticeService noticeService;

	@GetMapping("list")
	public R<List<Notify>> list() {
		//待办事项 前端的code
		String flowTodoCode = "flowTodo";
		String noticeCode = "notice";
		long flowTodoNum = wfProcessService.todoCount();
		long noticeNum = noticeService.unReadCount(AuthUtil.getUserId());
		ImmutableList<Notify> data = ImmutableList.<Notify>builder().add(new Notify(flowTodoCode, flowTodoNum))
			.add(new Notify(noticeCode, noticeNum)).build();
		return R.data(data);
	}

}
