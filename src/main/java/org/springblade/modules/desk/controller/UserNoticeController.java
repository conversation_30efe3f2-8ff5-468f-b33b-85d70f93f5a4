/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Collections;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.desk.dto.NoticeDTO;
import org.springblade.modules.desk.entity.UserNoticeEntity;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.desk.vo.NoticeVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户消息表 控制器
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("ni/desk/userNotice")
@Api(value = "用户消息表", tags = "用户消息表接口")
public class UserNoticeController extends BladeController {

	private final IUserNoticeService userNoticeService;

	/**
	 * 用户消息表 已读
	 */
	@PostMapping("/change2Read")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "已读", notes = "传入ids")
	public R change2Read(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userNoticeService.change2Read(Func.toLongList(ids)));
	}

	/**
	 * 用户消息表 自定义分页
	 */
	@GetMapping("/user-notice-page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入NoticeDTO")
	public R<IPage<NoticeVO>> userNoticePage(NoticeDTO notice, Query query) {
		Long userId = getUser().getUserId();
		IPage<NoticeVO> pages = userNoticeService
			.selectUserNoticeVOPage(Condition.getPage(query), notice, userId);
		return R.data(pages);
	}

	/**
	 * 用户消息表 update
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "update", notes = "传入NoticeVO")
	public R changeUserNotice(UserNoticeEntity userNotice) {
		return R.status(userNoticeService.updateById(userNotice));
	}

	@PostMapping("/read/{id}")
	public R<Void> read(@PathVariable("id") Long id,
		@RequestParam(required = false) String comment) {
		boolean res = userNoticeService.change2Read(Collections.singletonList(id), comment);
		return R.status(res);
	}
}
