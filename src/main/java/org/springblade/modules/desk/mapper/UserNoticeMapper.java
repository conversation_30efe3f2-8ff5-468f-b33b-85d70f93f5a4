/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.mapper;

import org.springblade.modules.desk.dto.NoticeDTO;
import org.springblade.modules.desk.entity.UserNoticeEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

import org.springblade.modules.desk.vo.NoticeVO;

/**
 * 用户消息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
public interface UserNoticeMapper extends BaseMapper<UserNoticeEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param notice
	 * @return
	 */
	List<NoticeVO> selectUserNoticeVOPage(IPage page, NoticeDTO notice, Long userId);

	/**
	 * 查询员工的未读的通知
	 *
	 * @param userId 用户id
	 * @return 未读消息数量
	 */
	Long getUnReadCount(Long userId);
}
