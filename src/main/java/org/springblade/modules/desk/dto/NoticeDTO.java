package org.springblade.modules.desk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.desk.entity.Notice;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeDTO extends Notice {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "发布时间-左区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime releaseTime_datege;
	@ApiModelProperty(value = "发布时间-右区间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime releaseTime_datelt;
	@ApiModelProperty(value = "是否已读 0否 1是")
	private Integer isRead;

}
