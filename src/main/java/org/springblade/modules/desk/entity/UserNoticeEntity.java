/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.desk.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 用户消息表 实体类
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@Data
@TableName("ni_user_notice")
@ApiModel(value = "UserNotice对象", description = "用户消息表")
public class UserNoticeEntity implements Serializable {

	/**
	 * 全体员工
	 */
	public static final Integer RELEASE_TARGET_ALL = 0;

	/**
	 * 指定员工
	 */
	public static final Integer RELEASE_TARGET_PART = 1;

	/**
	 * 公告状态-草稿
	 */
	public static final Integer NOTICE_STATUS_DRAFT = 0;
	/**
	 * 公告状态-发布
	 */
	public static final Integer NOTICE_STATUS_PUBLISH = 1;
	/**
	 * 公告状态-撤销
	 */
	public static final Integer NOTICE_STATUS_WITHDRAW = 2;

	/**
	 * 查阅状态-已读
	 */
	public static final Integer NOTICE_STATUS_READ = 1;

	/**
	 * 需要主管回复
	 */
	public static final Integer Need_Manager_Response = 1;
	private static final long serialVersionUID = -1461263843842610007L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 通知公告id
	 */
	@ApiModelProperty(value = "通知公告id")
	private Long noticeId;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long receiveUser;
	/**
	 * 是否已读 0否 1是
	 */
	@ApiModelProperty(value = "是否已读 0否 1是")
	private Integer isRead;
	/**
	 * 主管回复
	 */
	@ApiModelProperty(value = "主管回复")
	private String comment;
	/**
	 * 接收时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime receiveTime;

}
