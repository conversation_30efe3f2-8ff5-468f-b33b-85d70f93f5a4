
package org.springblade.modules.auth.granter;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.time.Duration;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.modules.auth.enums.UserEnum;
import org.springblade.modules.auth.provider.ITokenGranter;
import org.springblade.modules.auth.provider.TokenParameter;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.system.entity.Tenant;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.entity.UserOauth;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.ITenantService;
import org.springblade.modules.system.service.IUserOauthService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Component;

/**
 * 小程序
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MiniprogramTokenGranter implements ITokenGranter {

  public static final String GRANT_TYPE = "miniprogram";
  public static final Integer FAIL_COUNT = 5;
  public static final String FAIL_COUNT_VALUE = "account.failCount";
  private final IUserService userService;
  private final IRoleService roleService;
  private final ITenantService tenantService;
  private final BladeRedis bladeRedis;

  @Override
  public UserInfo grant(TokenParameter tokenParameter) {
    HttpServletRequest request = WebUtil.getRequest();
    // 获取用户绑定ID
    String headerDept = request.getHeader(TokenUtil.DEPT_HEADER_KEY);
    String headerRole = request.getHeader(TokenUtil.ROLE_HEADER_KEY);
    // 获取用户信息
    String tenantId = tokenParameter.getArgs().getStr("tenantId");
    String unionid = tokenParameter.getArgs().getStr("unionid");
    String openid = tokenParameter.getArgs().getStr("openid");

    UserInfo userInfo = null;
    // 获取租户信息
    Tenant tenant = tenantService.getByTenantId(tenantId);
    if (TokenUtil.judgeTenant(tenant)) {
      throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
    }
    // 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
    userInfo = userService.userInfoByMiniProgramOpenId(tenantId, openid, UserEnum.WEB);
    if (userInfo == null || userInfo.getUser() == null) {
      //外层处理，返回单独的code方便小程序判断
      return null;
    } else {
      // 判断登录是否锁定
      int cnt = Func.toInt(
          bladeRedis.get(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY,
              userInfo.getUser().getAccount())),
          0);
      int failCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
      if (cnt >= failCount) {
        throw new ServiceException(TokenUtil.USER_HAS_TOO_MANY_FAILS);
      }
      // 成功则清除登录错误次数
      bladeRedis.del(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY,
          userInfo.getUser().getAccount()));
    }
    // 多部门情况下指定单部门
    if (Func.isNotEmpty(headerDept) && userInfo != null && userInfo.getUser().getDeptId()
        .contains(headerDept)) {
      userInfo.getUser().setDeptId(headerDept);
    }
    // 多角色情况下指定单角色
    if (Func.isNotEmpty(headerRole) && userInfo != null && userInfo.getUser().getRoleId()
        .contains(headerRole)) {
      List<String> roleAliases = roleService.getRoleAliases(headerRole);
      userInfo.setRoles(roleAliases);
      userInfo.getUser().setRoleId(headerRole);
    }
    return userInfo;
  }

}
