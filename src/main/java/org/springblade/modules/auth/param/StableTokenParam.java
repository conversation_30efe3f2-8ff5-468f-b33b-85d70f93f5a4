package org.springblade.modules.auth.param;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取稳定版本的access_token传递的参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StableTokenParam {
	private String appid;
	private String secret;
	@JSONField(name = "grant_type")
	private String grantType;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
