package org.springblade.modules.auth.listener;

import static com.natergy.ni.product.service.ProductSkuService.CHECK_PRODUCT_MATERIAL_NEW_RECEIVE_ROLE_CODE;
import static org.springblade.modules.desk.entity.UserNoticeEntity.NOTICE_STATUS_READ;
import static org.springblade.modules.desk.entity.UserNoticeEntity.Need_Manager_Response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.natergy.ni.depot.entity.SteelOutEntity;
import com.natergy.ni.depot.service.ISteelOutService;
import com.natergy.ni.old.entity.ChdaEntity;
import com.natergy.ni.por.service.PorOrderArrivalService;
import com.natergy.ni.por.vo.PorOrderArrivalVO;
import com.natergy.ni.product.service.ProductSkuService;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.auth.event.LoginEvent;
import org.springblade.modules.desk.dto.NoticeDTO;
import org.springblade.modules.desk.service.IUserNoticeService;
import org.springblade.modules.desk.vo.NoticeVO;
import org.springblade.modules.system.entity.Dept;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;


@Slf4j
@Component
@RequiredArgsConstructor
public class LoginEventListener {

	private final PorOrderArrivalService arrivalService;
	private final IUserService userService;
	private final IUserDeptService userDeptService;
	private final IDeptService deptService;
	private final IUserNoticeService userNoticeService;
	private final ISteelOutService steelOutService;
	private final ProductSkuService productSkuService;

	@TransactionalEventListener(fallbackExecution = true)
	public void openEvent(LoginEvent event) {
		BladeUser user = event.getUser();
		log.debug("用户登录:{}", user.getAccount());
		loadUnInspection(user.getUserId());
		loadSteelOutUnAudit(user);
		loadUnInspection2Leader(user.getUserId());
//    loadUnInspection2GM(userId);
		if (user.getRoleName().contains(CHECK_PRODUCT_MATERIAL_NEW_RECEIVE_ROLE_CODE)) {
			loadNewProductMaterialCode(user.getUserId());
		}
		//查询需要回复的通知公告
		loadUserNoticeNeedReply(user.getUserId());
	}

	private void loadUserNoticeNeedReply(Long userId) {
		IPage<NoticeVO> page = new Page<>(1, 10);
		NoticeDTO notice = new NoticeDTO();
		notice.setIsRead(0);
		notice.setIsManagerResponse(Need_Manager_Response);
		IPage<NoticeVO> res = userNoticeService.selectUserNoticeVOPage(page, notice, userId);
		if (!res.getRecords().isEmpty()) {
			userNoticeService.sendSocket("/desk/notice-user", "待回复通知", "warning",
				String.format("您有%s条需回复的通知未回复，请及时回复", res.getTotal()),
				Collections.singletonList(userId));
		}
	}

	private void loadNewProductMaterialCode(Long userId) {
		List<ChdaEntity> chdaEntities = productSkuService.checkProductMaterialNew();
		if (chdaEntities.isEmpty()) {
			return;
		}
		StringBuilder content = new StringBuilder();
		chdaEntities.forEach(
			chda -> content.append(chda.getChdlmc()).append("(<strong style='color: red'>")
				.append(chda.getChdlbm())
				.append("</strong>)")
				.append("已新增<br/>"));
		userNoticeService.sendSocket("/ni/product/sku", "新增产成品编码通知", "warning",
			content.toString(),
			Collections.singletonList(userId));

	}

	/**
	 * 加载钢材出库未审核信息，并发送通知
	 *
	 * @param user
	 */
	private void loadSteelOutUnAudit(BladeUser user) {
		//判断是不是库管
		if (!StringUtil.containsAny(user.getRoleName(), new CharSequence[]{"wm"})) {
			return;
		}
		long draftNum = steelOutService.count(Wrappers.<SteelOutEntity>lambdaQuery()
			.eq(SteelOutEntity::getStatus, SteelOutEntity.STATUS_DRAFT));
		if (draftNum > 0) {
			String content = String.format("有【%s条】钢材出库需要审核", draftNum);
			userNoticeService.sendSocket("/ni/depot/steel-out", "钢材出库通知", "warning", content,
				Collections.singletonList(user.getUserId()));

		}
	}

	private void loadUnInspection2GM(Long userId) {
		User user = UserCache.getUser(userId);
	}

	/**
	 * 部门领导加载部门的未验收信息
	 *
	 * @param userId
	 */
	@Async
	public void loadUnInspection2Leader(Long userId) {
		User user = UserCache.getUser(userId);
		List<Dept> depts = deptService.list(
			Wrappers.<Dept>query().lambda().in(Dept::getLeaderId, user.getId()));
		if (!depts.isEmpty()) {
			List<Long> userIds = userDeptService.getUserIdsByDeptIds(
				depts.stream().map(Dept::getId).collect(Collectors.toSet()));
			//如果是臧令军 同时也查下陈英的申购
			if (user.getAccount().equals("臧令军")) {
				User chenYing = UserCache.getUser(BladeConstant.ADMIN_TENANT_ID, "陈英");
				if (chenYing != null) {
					userIds.add(chenYing.getId());
				}
			}
			userIds = userIds.stream().filter(id -> {
				List<PorOrderArrivalVO> arrivals = arrivalService.unInspectionList(id);
				if (arrivals == null || arrivals.isEmpty()) {
					return false;
				}
				arrivals = arrivals.stream()
					.filter(arrival -> DateUtil.fromDate(arrival.getCreateTime()).isBefore(
						LocalDateTime.now().plusDays(-3))).collect(Collectors.toList());
				return !arrivals.isEmpty();
			}).collect(Collectors.toList());
			if (userIds.isEmpty()) {
				return;
			}
			List<User> users = userService.listByIds(userIds);
			String usernames = users.stream().map(User::getRealName)
				.collect(Collectors.joining(","));
			String content = String.format(
				"您部门中<strong style='color: red'>%s</strong>还有超3天未验收的货物，请联系尽快处理！",
				usernames);
			userNoticeService.sendSocket("/ni/por/order-arrival-items", "部门未验收提醒", "warning",
				content,
				Collections.singletonList(userId));
		}
	}

	private boolean isLeader(User user) {
		long deptCount = deptService.count(
			Wrappers.<Dept>query().lambda().in(Dept::getLeaderId, user.getId()));
		return deptCount > 0;
	}

	/**
	 * 加载未验收信息，并发送通知
	 *
	 * @param userId
	 */
	@Async
	public void loadUnInspection(Long userId) {
		List<PorOrderArrivalVO> arrival = arrivalService.unInspectionList(userId);
		if (arrival == null || arrival.isEmpty()) {
			return;
		}
		String content = "";
		if (arrival.size() == 1) {
			content = String.format(
				"您申购的<strong style='color: red'>%s</strong>还未验收，请尽快处理！",
				arrival.get(0).getMaterialName());
		} else {
			content = String.format(
				"您有【<strong style='color: red'>%s</strong>】条到货明细未验收，请尽快处理！",
				arrival.size());
		}
		userNoticeService.sendSocket("/ni/por/order-arrival-items", "到货未验收提醒", "warning",
			content,
			Collections.singletonList(userId));
	}
}
