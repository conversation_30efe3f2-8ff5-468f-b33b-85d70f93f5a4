package org.springblade.modules.auth.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/08/11
 * @desc 获取场景二维码需要传输参数
 */
@Data
public class GenerateQrcodeDTO {

	private Long expireSeconds;

	private String actionName;

	private ActionInfo actionInfo;

	@Data
	public static class ActionInfo {

		private Scene scene;

		@Data
		public static class Scene {

			private String sceneStr;


		}
	}

}
