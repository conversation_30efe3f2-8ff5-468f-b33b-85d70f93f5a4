package org.springblade.modules.auth.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/05
 * @desc 通过网页来获取openid
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OffiaccountWebDTO {

	@JSONField(name="openid")
	private String openid;
	@JSONField(name="scope")
	private String scope;
}
