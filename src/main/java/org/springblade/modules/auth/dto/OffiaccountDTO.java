package org.springblade.modules.auth.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.beans.CopyProperty;

/**
 * <AUTHOR>
 * @date 2023/08/11
 * @desc 用来传输获取的微信公众号的access_token
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OffiaccountDTO {

	@JSONField(name="access_token")
	private String accessToken;
	@JSONField(name="expires_in")
	private Long expiresIn;
}
