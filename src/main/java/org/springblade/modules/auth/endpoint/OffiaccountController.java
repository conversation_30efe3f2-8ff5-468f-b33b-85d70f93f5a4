/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.endpoint;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.UserCache;
import org.springblade.core.cache.constant.CacheConstant;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.auth.dto.MstDataXmlDTO;
import org.springblade.modules.auth.dto.OffiaccountDTO;
import org.springblade.modules.auth.dto.OffiaccountWebDTO;
import org.springblade.modules.auth.dto.QrcodeInfoDTO;
import org.springblade.modules.auth.enums.OffiaccountEnum;
import org.springblade.modules.auth.service.MiniprogramService;
import org.springblade.modules.auth.utils.SignUtil;
import org.springblade.modules.auth.utils.XmlUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
@Slf4j
@RestController
@RequestMapping(AppConstant.APPLICATION_AUTH_NAME)
@Api(value = "微信公众号回调接口", tags = "微信公众号回调接口")
@Validated
public class OffiaccountController {
	@Value("${wechat.offiaccount.callToken}")
	private String callToken;

	/**
	 * 通过场景码进行订阅的，EVENTKEY的前缀
	 */
	private final static String EVENT_KEY_PREFIX = "qrscene_";

	private final BladeRedis bladeRedis;

	private final MiniprogramService miniprogramService;

	private final IUserService iUserService;

	public OffiaccountController(BladeRedis bladeRedis, MiniprogramService miniprogramService, IUserService iUserService) {
		this.bladeRedis = bladeRedis;
		this.miniprogramService = miniprogramService;
		this.iUserService = iUserService;
	}


	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取微信公众号场景二维码", notes = "传入id")
	@GetMapping("/oauth/getBindOffiaccountQrcode")
	public R<Object> getBindOffiaccountQrcode() {
		Long userId = AuthUtil.getUserId();

		OffiaccountDTO offiaccountAccessToken = miniprogramService.getOffiaccountAccessToken();

		Objects.requireNonNull(offiaccountAccessToken.getAccessToken(), "获取accessToken失败");

		//先从redis中读取场景二维码，没有的话再从api中获取
		Object qrcodeImage = bladeRedis.get(CacheNames.OFFIACCOUNT_QRCODE_URL_KEY + userId);

		if (Objects.isNull(qrcodeImage)) {

			QrcodeInfoDTO qrcode = miniprogramService.getQrcode(userId, offiaccountAccessToken.getAccessToken());

			qrcodeImage = qrcode.getTicket();
		}

		return R.data(qrcodeImage);
	}

	@ApiOperation(value = "回调接口")
	@GetMapping("/offiaccount/callback")
	public String token(@RequestParam String signature, @RequestParam String timestamp, @RequestParam String nonce, @RequestParam String echostr) {
		if (SignUtil.checkSignature(callToken, signature, timestamp, nonce)) {
			return echostr;
		}
		return "error";
	}


	/**
	 * 回调接口
	 */
	@ApiOperation(value = "回调接口")
	@PostMapping(value = "/offiaccount/callback")
	public String offiaccountCallBack(@RequestBody MstDataXmlDTO dataXml, @RequestParam String signature, @RequestParam String timestamp, @RequestParam String nonce) {

		if (!SignUtil.checkSignature(callToken, signature, timestamp, nonce)) {
			return "error";
		}

		String userId = null;

		//订阅事件
		if (OffiaccountEnum.SUBSCRIBE.getValue().equals(dataXml.getEvent())) {
			String eventKey = dataXml.getEventKey();
			//不存在eventkey不处理
			if (StringUtil.isBlank(eventKey)) {
				return Strings.EMPTY;
			}
			//没有传入eventkey的前缀不处理
			if (!eventKey.contains(EVENT_KEY_PREFIX)) {
				return Strings.EMPTY;
			}

			userId = eventKey.substring(EVENT_KEY_PREFIX.length());

		}

		//扫描场景码事件
		if (OffiaccountEnum.SCAN.getValue().equals(dataXml.getEvent())) {

			userId = dataXml.getEventKey();
		}

		//为用户设置上对应的openId
		if (Objects.nonNull(userId)) {
			User user = new User();
			user.setOpenId(dataXml.getFromUserName());
			user.setId(Long.parseLong(userId));
			iUserService.updateById(user);
			CacheUtil.clear(CacheConstant.USER_CACHE);
			User sendUser = UserCache.getUser(user.getId());
			try {
				dataXml.setContent(sendUser.getAccount() + "，您好！您成功绑定此公众号");
				String fromUserName = dataXml.getFromUserName();
				dataXml.setFromUserName(dataXml.getToUserName());
				dataXml.setToUserName(fromUserName);
				dataXml.setMsgType("text");
				return XmlUtil.convertObjectToXml(dataXml);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}
		}

		//取消关注公众号事件
		if (OffiaccountEnum.UNSUBSCRIBE.getValue().equals(dataXml.getEvent())) {
			iUserService.unSubscribe(dataXml.getFromUserName());
		}


		return Strings.EMPTY;
	}


	@ApiOperation(value = "获取网页授权的AccessToken和opendId", notes = "传入code")
	@GetMapping("/oauth/getWebAccessTokenAndOpenId")
	public R<OffiaccountWebDTO> getWebAccessTokenAndOpenId(@RequestParam @NotEmpty(message = "code不能为空") String code) {

		OffiaccountWebDTO offiaccountWebDTO = miniprogramService.getWebAccessTokenAndOpenId(code);

		return R.data(offiaccountWebDTO);
	}


}
