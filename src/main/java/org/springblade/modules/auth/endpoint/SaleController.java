/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.endpoint;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.api.crypto.annotation.decrypt.ApiDecryptAes;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.modules.auth.dto.OffiaccountDTO;
import org.springblade.modules.auth.param.SaleInfoParam;
import org.springblade.modules.auth.service.MiniprogramService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;


@Slf4j
@RestController
@RequestMapping(AppConstant.APPLICATION_AUTH_NAME)

@Api(value = "销售平台获取access_token", tags = "获取token的")
public class SaleController {

	private final MiniprogramService miniprogramService;

	private final IUserService userService;

	public SaleController(MiniprogramService miniprogramService,IUserService userService) {
		this.miniprogramService = miniprogramService;
		this.userService=userService;
	}

	@ApiOperation(value = "获取微信公众平台的token信息")
	@PostMapping("/oauth/getOffiaccount")
	public R<Object> getBindOffiaccountQrcode(@ApiDecryptAes SaleInfoParam saleInfo) {
		if (Objects.isNull(saleInfo.getPlatform()) || Objects.isNull(saleInfo.getAuthorizedUser())) {
			return R.fail("非法请求");
		}

		OffiaccountDTO offiaccountAccessToken = miniprogramService.getOffiaccountAccessToken();

		Objects.requireNonNull(offiaccountAccessToken.getAccessToken(), "获取accessToken失败");

		return R.data(offiaccountAccessToken);
	}


}
