/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.auth.endpoint;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.google.common.collect.ImmutableMap;
import com.wf.captcha.SpecCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.SysCache;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.http.HttpRequest;
import org.springblade.core.http.util.HttpUtil;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Base64Util;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.modules.auth.event.LoginEvent;
import org.springblade.modules.auth.provider.ITokenGranter;
import org.springblade.modules.auth.provider.TokenGranterBuilder;
import org.springblade.modules.auth.provider.TokenParameter;
import org.springblade.modules.auth.service.MiniprogramService;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.entity.UserInfo;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static org.springblade.core.cache.constant.CacheConstant.*;

/**
 * 令牌端点
 *
 * <AUTHOR>
 */
@NonDS
@ApiSort(1)
@RestController
@RequiredArgsConstructor
@RequestMapping(AppConstant.APPLICATION_AUTH_NAME)
@Api(value = "用户授权认证", tags = "授权接口")
public class BladeTokenEndPoint {

  private final BladeRedis bladeRedis;
  private final JwtProperties jwtProperties;
  private final MiniprogramService miniprogramService;
  private final IUserService userService;


  @ApiLog("登录用户验证")
  @PostMapping("/oauth/token")
  @ApiOperation(value = "获取认证令牌", notes = "传入租户ID:tenantId,账号:account,密码:password")
  public Kv token(@ApiParam(value = "租户ID", required = true) @RequestParam String tenantId,
      @ApiParam(value = "账号", required = true) @RequestParam(required = false) String username,
      @ApiParam(value = "密码", required = true) @RequestParam(required = false) String password,
      @ApiParam(value = "手机号", required = true) @RequestParam(required = false) String phone,
      @ApiParam(value = "手机验证码", required = true) @RequestParam(required = false) String code,
      @ApiIgnore @RequestHeader(name = TokenUtil.DEPT_HEADER_KEY, required = false) String deptId,
      @ApiIgnore @RequestHeader(name = TokenUtil.ROLE_HEADER_KEY, required = false) String roleId) {

    Kv authInfo = Kv.create();

    String grantType = WebUtil.getRequest().getParameter("grant_type");
    String refreshToken = WebUtil.getRequest().getParameter("refresh_token");

    String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY),
        TokenUtil.DEFAULT_USER_TYPE);

    TokenParameter tokenParameter = new TokenParameter();
    tokenParameter.getArgs().set("tenantId", tenantId).set("username", username)
        .set("password", password).set("phone", phone).set("code", code).set("grantType", grantType)
        .set("refreshToken", refreshToken).set("userType", userType).set("deptId", deptId)
        .set("roleId", roleId);

    ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
    UserInfo userInfo = granter.grant(tokenParameter);
    if (userInfo != null && !userInfo.getUser().getStatus().equals(User.STATUS_ON)) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "未获取到在职的员工信息，请联系管理员");
    }
    if (userInfo == null || userInfo.getUser() == null) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "用户名或密码/手机号不正确");
    }

    if (Func.isEmpty(userInfo.getRoles())) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "未获得用户的角色信息");
    }

    return TokenUtil.createAuthInfo(userInfo);
  }


  @GetMapping("/oauth/logout")
  @ApiOperation(value = "退出登录")
  public Kv logout() {
    BladeUser user = AuthUtil.getUser();
    if (user != null && jwtProperties.getState()) {
      String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
      JwtUtil.removeAccessToken(user.getTenantId(), String.valueOf(user.getUserId()), token);
    }
    return Kv.create().set("success", "true").set("msg", "success").set("code", 200);
  }

  @GetMapping("/oauth/captcha")
  @ApiOperation(value = "获取验证码")
  public Kv captcha() {
    SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
    String verCode = specCaptcha.text().toLowerCase();
    String key = UUID.randomUUID().toString();
    // 存入redis并设置过期时间为30分钟
    bladeRedis.setEx(CacheNames.CAPTCHA_KEY + key, verCode, Duration.ofMinutes(30));
    // 将key和base64返回给前端
    return Kv.create().set("key", key).set("image", specCaptcha.toBase64());
  }

  @GetMapping("/oauth/clear-cache")
  @ApiOperation(value = "清除缓存")
  public Kv clearCache() {
    CacheUtil.clear(BIZ_CACHE);
    CacheUtil.clear(USER_CACHE);
    CacheUtil.clear(USER_CACHE, false);
    CacheUtil.clear(DICT_CACHE);
    CacheUtil.clear(FLOW_CACHE);
    CacheUtil.clear(SYS_CACHE);
    CacheUtil.clear(PARAM_CACHE);
    CacheUtil.clear(RESOURCE_CACHE);
    CacheUtil.clear(MENU_CACHE);
    CacheUtil.clear(DICT_CACHE, Boolean.FALSE);
    CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
    CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
    CacheUtil.clear(PARAM_CACHE, Boolean.FALSE);
    return Kv.create().set("success", "true").set("msg", "success");
  }

  @Value("${wechat.miniprogram.appId}")
  public String appId;
  @Value("${wechat.miniprogram.appSecret}")
  public String appSecret;
  @Value("${wechat.miniprogram.url}")
  public String url;

  @ApiLog("小程序登陆")
  @PostMapping("/oauth/miniprogram/login1")
  @ApiOperation(value = "小程序登陆", notes = "传入租户ID:tenantId,账号:account,密码:password")
  public Kv miniprogramLogin(
      @ApiParam(value = "租户ID", required = false) @RequestParam(required = false) String tenantId,
      @ApiParam(value = "登录时获取的 code，可通过wx.login获取", required = true) @RequestParam(value = "code", required = true) String jsCode) {
    Kv authInfo = Kv.create();
    Map<String, Object> map = ImmutableMap.of("appid", appId, "secret", appSecret, "js_code",
        jsCode, "grant_type", "authorization_code");
    String data = HttpUtil.get(url, map);
    Map<String, Object> res = JsonUtil.parse(data, new TypeReference<Map<String, Object>>() {
    });
    Integer errcode = (Integer) res.get("errcode");
    if (errcode != null && errcode != 0) {
      return authInfo.set("error_code", errcode).set("error_description", res.get("errmsg"));
    }
    String unionid = (String) res.get("unionid");
    String openid = (String) res.get("openid");
    TokenParameter tokenParameter = new TokenParameter();
    tokenParameter.getArgs().set("tenantId",
            StringUtils.isNoneBlank(tenantId) ? tenantId : BladeConstant.ADMIN_TENANT_ID)
        .set("unionid", unionid).set("openid", openid);
    ITokenGranter granter = TokenGranterBuilder.getGranter("miniprogram");
    UserInfo userInfo = granter.grant(tokenParameter);

    if (userInfo == null || userInfo.getUser() == null) {
      return authInfo.set("error_code", 410)
          .set("error_description", "未查询到绑定的用户信息，请先绑定用户");
    }
    if (Func.isEmpty(userInfo.getRoles())) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "未获得用户的角色信息");
    }
    return TokenUtil.createAuthInfo(userInfo);
  }

  @Value("${wechat.miniprogram.phoneNumberUrl}")
  public String phoneNumberUrl;

  @PostMapping("/oauth/miniprogram/login")
  @ApiOperation(value = "小程序手机号登陆")
  public Kv miniprogramPhoneLogin(
      @ApiParam(value = "租户ID", required = false) @RequestParam(required = false) String tenantId,
      @ApiParam(value = "登录时获取的 code，可通过wx.login获取", required = true) @RequestParam(value = "code", required = true) String code,
      @ApiIgnore @RequestHeader(name = TokenUtil.DEPT_HEADER_KEY, required = false) String deptId,
      @ApiIgnore @RequestHeader(name = TokenUtil.ROLE_HEADER_KEY, required = false) String roleId) {
    Kv authInfo = Kv.create();
    String accessToken = bladeRedis.get(CacheNames.MINI_PROGRAM_ACCESS_TOKEN_KEY);
    if (accessToken == null || !StringUtils.isBlank(accessToken)) {
      accessToken = miniprogramService.refreshToken();
    }
    Map<String, Object> map = ImmutableMap.of("code", code);
    Map<String, Object> res = HttpRequest.post(phoneNumberUrl + "?access_token=" + accessToken)
        .bodyJson(map).execute().onSuccess(r -> r.asMap(Object.class));
    Integer errcode = (Integer) res.get("errcode");
    if (errcode != null && errcode != 0) {
      return authInfo.set("error_code", errcode).set("error_description", res.get("errmsg"));
    }
    Map<String, Object> phoneInfo = (Map<String, Object>) res.get("phone_info");
    authInfo.set("phoneNumber", phoneInfo.get("phoneNumber"));

    String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY),
        TokenUtil.DEFAULT_USER_TYPE);
    TokenParameter tokenParameter = new TokenParameter();
    tokenParameter.getArgs().set("tenantId", tenantId).set("phone", phoneInfo.get("phoneNumber"))
        .set("userType", userType).set("deptId", deptId).set("roleId", roleId);

    ITokenGranter granter = TokenGranterBuilder.getGranter("miniprogramPhone");
    UserInfo userInfo = granter.grant(tokenParameter);
    if (userInfo == null || userInfo.getUser() == null) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "非内部员工，请联系管理员");
    }

    if (Func.isEmpty(userInfo.getRoles())) {
      return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST)
          .set("error_description", "未获得用户的角色信息");
    }

    return TokenUtil.createAuthInfo(userInfo);
  }

  /**
   * 查询单条
   */
  @ApiOperationSupport(order = 2)
  @ApiOperation(value = "查看详情", notes = "传入id")
  @GetMapping("/oauth/user-info")
  public R<Kv> info(BladeUser user) {
    UserInfo userInfo = userService.userInfo(user.getUserId());
    User user1 = userInfo.getUser();
    UserVO res = UserWrapper.build().entityVO(user1);
    user1.setAvatar(res.getAvatarUrl());
    Kv authInfo = Kv.create();
    try {
      authInfo.set(TokenConstant.TENANT_ID, user1.getTenantId())
          .set(TokenConstant.USER_ID, Func.toStr(user1.getId()))
          .set(TokenConstant.DEPT_ID, user1.getDeptId())
          .set(TokenConstant.POST_ID, user1.getPostId())
          .set(TokenConstant.ROLE_ID, user1.getRoleId())
          .set(TokenConstant.OAUTH_ID, userInfo.getOauthId())
          .set(TokenConstant.ACCOUNT, user1.getAccount())
          .set(TokenConstant.USER_NAME, user1.getAccount())
          .set(TokenConstant.NICK_NAME, user1.getRealName())
          .set(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()))
          .set(TokenConstant.AVATAR, Func.toStr(user1.getAvatar(), TokenConstant.DEFAULT_AVATAR))
          .set(TokenConstant.DETAIL, userInfo.getDetail())
          .set("dept_name", SysCache.getDeptNames(user1.getDeptId()))
          .set("post_name", SysCache.getPostNames(user1.getPostId()))
          .set("qr_code", Base64Util.encode(user1.getAccount(), StandardCharsets.UTF_8));
    } catch (Exception ex) {
      authInfo.set("error_code", HttpServletResponse.SC_UNAUTHORIZED)
          .set("error_description", ex.getMessage());
    }
    return R.data(authInfo);
  }

  @GetMapping("/oauth/login-event")
  @ApiOperation(value = "登录事件")
  public R loginEvent() {
    if (AuthUtil.getUser() != null) {
      SpringUtil.publishEvent(new LoginEvent(AuthUtil.getUser()));
    }
    return R.success("");
  }

}
