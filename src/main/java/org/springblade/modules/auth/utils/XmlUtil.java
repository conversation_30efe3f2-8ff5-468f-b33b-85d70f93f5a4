package org.springblade.modules.auth.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/8/14
 */
public class XmlUtil {

	public static String convertObjectToXml(Object object) throws JsonProcessingException {
		Objects.requireNonNull(object, "要转为bean的对象不可为空");
		XmlMapper xmlMapper = new XmlMapper();
		//设置xml格式携带版本和编码信息<?xml version='1.0' encoding='UTF-8'?>
		xmlMapper.enable(ToXmlGenerator.Feature.WRITE_XML_DECLARATION);
		//字段值为null，自动忽略，不再序列化
		xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		return xmlMapper.writeValueAsString(object);

	}

}
