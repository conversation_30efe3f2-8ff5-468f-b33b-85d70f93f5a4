package org.springblade.modules.auth.service;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/09/14
 * @desc 微信公众模板发送消息
 */
public class WeChatTemplateMessage {
	private static final long serialVersionUID = 5063374783759519418L;

	private Builder builder;

	private WeChatTemplateMessage() {

	}

	private WeChatTemplateMessage(Builder builder) {
		this.builder = builder;
	}

	public void sendMsg(Consumer<String> consumer) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			consumer.accept(objectMapper.writeValueAsString(this.builder));
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	public static class Builder {
		private String touser;
		private String template_id;
		private String url;
		private MiniProgram miniprogram;
		private List<Map<String, Map<String, Object>>> data;

		public WeChatTemplateMessage builder() {
			return new WeChatTemplateMessage(this);
		}

		public Builder setTouser(String touser) {
			this.touser = touser;
			return this;
		}

		public Builder setTemplate_id(String template_id) {
			this.template_id = template_id;
			return this;
		}

		public Builder setUrl(String url) {
			this.url = url;
			return this;
		}

		public Builder setMiniprogram(MiniProgram miniprogram) {
			this.miniprogram = miniprogram;
			return this;
		}

		public Builder setDataValue(List<Map<String, Object>> dataValue) {
			this.data = dataValue.stream().map(item -> item.entrySet().stream()
				.collect(Collectors.toMap(Map.Entry::getKey, (entryItem) -> {
					Map<String, Object> objectObjectHashMap = new HashMap<>();
					objectObjectHashMap.put("value", entryItem.getValue());
					return objectObjectHashMap;
				}))).collect(Collectors.toList());
			return this;
		}

		public String getTouser() {
			return touser;
		}

		public String getTemplate_id() {
			return template_id;
		}

		public String getUrl() {
			return url;
		}

		public MiniProgram getMiniprogram() {
			return miniprogram;
		}

		public Map<String, Map<String, Object>> getData() {

			return data.stream().flatMap(map -> map.entrySet().stream())
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

		}

	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class MiniProgram implements Serializable {
		private static final long serialVersionUID = -7945254706501974849L;
		private String appid;
		private String pagepath;
	}


}
