package org.springblade.modules.auth.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.RestTemplateUtil;
import org.springblade.core.http.util.HttpUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.modules.auth.dto.GenerateQrcodeDTO;
import org.springblade.modules.auth.dto.OffiaccountDTO;
import org.springblade.modules.auth.dto.OffiaccountWebDTO;
import org.springblade.modules.auth.dto.QrcodeInfoDTO;
import org.springblade.modules.auth.param.StableTokenParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> And Flyjar
 * @version 1.0
 * @since 2023/4/26 13:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniprogramService {

	@Value("${wechat.miniprogram.accessTokenUrl}")
	private String accessTokenUrl;

	@Value("${wechat.miniprogram.appId}")
	public String appId;
	@Value("${wechat.miniprogram.appSecret}")
	public String appSecret;
	@Value("${wechat.miniprogram.url}")
	public String url;


	@Value("${wechat.offiaccount.accessTokenUrl}")
	private String offiaccountAccessTokenUrl;


	@Value("${wechat.offiaccount.qrcodeUrl}")
	private String offiaccountGetQrcodeUrl;

	@Value("${wechat.offiaccount.qrcodeImage}")
	private String offiaccountQrcodeImage;

	@Value("${wechat.offiaccount.appId}")
	public String offiaccountAppId;

	@Value("${wechat.offiaccount.appSecret}")
	private String offiaccountAppSecret;

	@Value("${wechat.offiaccount.grantType}")
	private String offiaccountGrantType;


	@Value("${wechat.offiaccount.sendTemplateMsg}")
	private String sendTempUrl;


	@Value("${wechat.offiaccount.webAccessToken}")
	private String webAccessTokenUrl;

	@Value("${wechat.offiaccount.webGrantType}")
	private String webGrantType;


	private final BladeRedis bladeRedis;

	/**
	 * 刷新token
	 *
	 * @return String 获取到的小程序的accesstoken
	 */
	public String refreshToken() {
		String data = HttpUtil.get(accessTokenUrl, ImmutableMap.of("grant_type", "client_credential", "appid", appId, "secret", appSecret));
		log.info(data);
		Map<String, Object> res = JsonUtil.parse(data, new TypeReference<Map<String, Object>>() {
		});

		Integer errcode = (Integer) res.get("errcode");
		if (errcode != null && errcode != 0) {
			log.error("请求小程序token失败:{}", data);
			throw new ServiceException("token请求失败");
		}
		String accessToken = (String) res.get("access_token");

		int expiresIn = res.get("expires_in") != null ? (Integer) res.get("expires_in") : 7200;
		//缓存
		bladeRedis.setEx(CacheNames.MINI_PROGRAM_ACCESS_TOKEN_KEY, accessToken, Duration.ofSeconds(expiresIn));
		return accessToken;
	}


	/**
	 * 获取AccessToken
	 * @return OffiaccountDTO
	 */
	public OffiaccountDTO getOffiaccountAccessToken() {

		//先读取redis中的accesstoken，没有的话再去从api中获取
		Object accessToken = bladeRedis.get(CacheNames.OFFIACCOUNT_ACCESS_TOKEN_KEY);

		if (Objects.nonNull(accessToken)) {
			return JSON.parseObject(accessToken.toString(), OffiaccountDTO.class);
		}

		String params = MessageFormat.format("?grant_type={0}&appid={1}&secret={2}", offiaccountGrantType, offiaccountAppId, offiaccountAppSecret);

		ResponseEntity<String> responseEntity = RestTemplateUtil.get(offiaccountAccessTokenUrl + params, String.class);

		String body = responseEntity.getBody();

		log.info(body);

		OffiaccountDTO dataInfo = JSON.parseObject(body, OffiaccountDTO.class);

		Objects.requireNonNull(dataInfo, "获取公众号的accessToken失败");

		//缓存到redis
		bladeRedis.setEx(CacheNames.OFFIACCOUNT_ACCESS_TOKEN_KEY, body, dataInfo.getExpiresIn());

		return dataInfo;
	}


	/**
	 * 通过getStableAccessToken获取微信公众号的accessToken
	 * @return OffiaccountDTO 微信公众号的accessToken信息
	 */
	public OffiaccountDTO getOffiaccountStableAccessToken() {

		StableTokenParam stableTokenParam = new StableTokenParam(appId, appSecret, "client_credential");

		ResponseEntity<String> responseEntity = RestTemplateUtil.post(offiaccountAccessTokenUrl, MediaType.APPLICATION_JSON, String.class, stableTokenParam.toString());

		String body = responseEntity.getBody();

		log.info(body);

		OffiaccountDTO dataInfo = JSON.parseObject(body, OffiaccountDTO.class);

		Objects.requireNonNull(dataInfo, "获取公众号稳定版本的accessToken失败");

		return dataInfo;
	}


	/**
		 * 根据accessToken生成场景二维码
		 *
		 * @param userId      场景二维码传递的参数
		 * @param accessToken 获取到的微信公众号的accessToken
		 * @return 场景二维码信息
		 */
	public QrcodeInfoDTO getQrcode(Long userId, String accessToken) {

		String params = MessageFormat.format("?access_token={0}", accessToken);

		GenerateQrcodeDTO generateQrcodeDTO = new GenerateQrcodeDTO();

		generateQrcodeDTO.setExpireSeconds(2592000L);

		generateQrcodeDTO.setActionName("QR_STR_SCENE");

		GenerateQrcodeDTO.ActionInfo actionInfo = new GenerateQrcodeDTO.ActionInfo();

		GenerateQrcodeDTO.ActionInfo.Scene scene = new GenerateQrcodeDTO.ActionInfo.Scene();

		scene.setSceneStr(userId.toString());

		actionInfo.setScene(scene);

		generateQrcodeDTO.setActionInfo(actionInfo);

		//将小驼峰转成蛇形驼峰
		SerializeConfig config = new SerializeConfig();

		config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;

		String jsonString = JSON.toJSONString(generateQrcodeDTO, config);

		log.info(jsonString);

		ResponseEntity<String> responseEntity = RestTemplateUtil.post(offiaccountGetQrcodeUrl + params, MediaType.APPLICATION_JSON, jsonString, String.class);

		String body = responseEntity.getBody();

		log.info(body);

		QrcodeInfoDTO dataInfo = JSON.parseObject(body, QrcodeInfoDTO.class);

		Objects.requireNonNull(dataInfo, "生成场景二维码失败");

		Objects.requireNonNull(dataInfo.getTicket(), "获取场景二维码失败");

		String encodeTicket = URLEncoder.encode(dataInfo.getTicket());

		dataInfo.setTicket(offiaccountQrcodeImage + encodeTicket);

		//缓存到redis
		bladeRedis.setEx(CacheNames.OFFIACCOUNT_QRCODE_URL_KEY + userId, dataInfo.getTicket(), dataInfo.getExpireSeconds());

		return dataInfo;

	}


	public void sendTemp(String jsonData, String accessToken) {

		log.info(jsonData);

		String params = MessageFormat.format("?access_token={0}", accessToken);

		ResponseEntity<String> responseEntity = RestTemplateUtil.post(sendTempUrl + params, MediaType.APPLICATION_JSON, jsonData, String.class);

		log.info(responseEntity.getBody());
	}




	/**
	 * 获取网页授权的AccessToken和opendId
	 * @return OffiaccountDTO
	 */
	public OffiaccountWebDTO getWebAccessTokenAndOpenId(String code) {

		String url = MessageFormat.format("{0}?grant_type={1}&code={2}&appid={3}&secret={4}",webAccessTokenUrl, webGrantType,code, offiaccountAppId, offiaccountAppSecret);

		log.info(url);

		ResponseEntity<String> responseEntity = RestTemplateUtil.get(url, String.class);

		String body = responseEntity.getBody();

		log.info(body);

		OffiaccountWebDTO dataInfo = JSON.parseObject(body, OffiaccountWebDTO.class);

		Objects.requireNonNull(dataInfo, "获取网页授权的AccessToken和opendId失败");

		return dataInfo;
	}


}
